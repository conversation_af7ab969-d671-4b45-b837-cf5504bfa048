// Database migrations and schema management
import { db } from "./db";
import { seedDatabase } from "./seed";

// Migration interface
interface Migration {
  version: number;
  name: string;
  up: () => Promise<void>;
  down: () => Promise<void>;
}

// Migration 001: Initial schema setup
const migration001: Migration = {
  version: 1,
  name: "initial_schema",
  up: async () => {
    console.log("Running migration 001: Initial schema setup");
    
    // TanStack DB handles schema creation automatically
    // We just need to seed the initial data
    await seedDatabase();
    
    console.log("Migration 001 completed");
  },
  down: async () => {
    console.log("Rolling back migration 001");
    // Clear all collections
    // Note: In a real app, you'd want more sophisticated rollback logic
    console.log("Migration 001 rollback completed");
  },
};

// All migrations in order
const migrations: Migration[] = [
  migration001,
];

// Get current migration version
async function getCurrentVersion(): Promise<number> {
  try {
    // In a real app, you'd store this in a migrations table
    // For now, we'll use localStorage or return 0
    const version = localStorage.getItem("db_migration_version");
    return version ? parseInt(version, 10) : 0;
  } catch {
    return 0;
  }
}

// Set current migration version
async function setCurrentVersion(version: number): Promise<void> {
  try {
    localStorage.setItem("db_migration_version", version.toString());
  } catch (error) {
    console.error("Failed to set migration version:", error);
  }
}

// Run pending migrations
export async function runMigrations(): Promise<void> {
  try {
    const currentVersion = await getCurrentVersion();
    const pendingMigrations = migrations.filter(m => m.version > currentVersion);
    
    if (pendingMigrations.length === 0) {
      console.log("No pending migrations");
      return;
    }
    
    console.log(`Running ${pendingMigrations.length} pending migrations...`);
    
    for (const migration of pendingMigrations) {
      console.log(`Running migration ${migration.version}: ${migration.name}`);
      await migration.up();
      await setCurrentVersion(migration.version);
    }
    
    console.log("All migrations completed successfully");
  } catch (error) {
    console.error("Migration failed:", error);
    throw error;
  }
}

// Rollback to specific version
export async function rollbackToVersion(targetVersion: number): Promise<void> {
  try {
    const currentVersion = await getCurrentVersion();
    
    if (targetVersion >= currentVersion) {
      console.log("Target version is not lower than current version");
      return;
    }
    
    const migrationsToRollback = migrations
      .filter(m => m.version > targetVersion && m.version <= currentVersion)
      .sort((a, b) => b.version - a.version); // Rollback in reverse order
    
    console.log(`Rolling back ${migrationsToRollback.length} migrations...`);
    
    for (const migration of migrationsToRollback) {
      console.log(`Rolling back migration ${migration.version}: ${migration.name}`);
      await migration.down();
    }
    
    await setCurrentVersion(targetVersion);
    console.log(`Rollback to version ${targetVersion} completed`);
  } catch (error) {
    console.error("Rollback failed:", error);
    throw error;
  }
}

// Initialize database
export async function initializeDatabase(): Promise<void> {
  try {
    console.log("Initializing database...");
    await runMigrations();
    console.log("Database initialization completed");
  } catch (error) {
    console.error("Database initialization failed:", error);
    throw error;
  }
}