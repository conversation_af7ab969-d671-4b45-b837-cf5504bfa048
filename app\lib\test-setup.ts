// Test setup and database seeding
import { seedDatabase } from './seed';
import { registerUser } from './auth';

// Test data for seeding
export async function setupTestData() {
  try {
    console.log('🌱 Setting up test data...');
    
    // Seed the database with contest structure
    await seedDatabase();
    
    // Create test users
    console.log('👤 Creating test users...');
    
    // Create admin user
    const adminResult = await registerUser({
      email: '<EMAIL>',
      password: 'Admin123!',
      firstName: 'Admin',
      lastName: 'User',
    });
    
    if (adminResult.success && adminResult.user) {
      // Update user to be admin (since we can't set this in registration)
      const { usersCollection } = await import('./collections');
      await usersCollection.update(adminResult.user.id, {
        ...adminResult.user,
        isAdmin: true,
        updatedAt: new Date(),
      });
      console.log('✅ Admin user created: <EMAIL> / Admin123!');
    }
    
    // Create regular user
    const userResult = await registerUser({
      email: '<EMAIL>',
      password: 'User123!',
      firstName: 'Test',
      lastName: 'User',
    });
    
    if (userResult.success) {
      console.log('✅ Regular user created: <EMAIL> / User123!');
    }
    
    console.log('🎉 Test data setup complete!');
    console.log('\n📋 Test Accounts:');
    console.log('Admin: <EMAIL> / Admin123!');
    console.log('User:  <EMAIL> / User123!');
    
  } catch (error) {
    console.error('❌ Error setting up test data:', error);
    throw error;
  }
}

// Reset test data
export async function resetTestData() {
  try {
    console.log('🔄 Resetting test data...');
    
    // Clear all collections
    const { 
      usersCollection,
      contestsCollection,
      categoriesCollection,
      classificationsCollection,
      competitionStateCollection 
    } = await import('./collections');
    
    // Note: TanStack DB doesn't have a clear method, so we'll need to implement this differently
    // For now, we'll just log that we would reset the data
    console.log('⚠️  Reset functionality needs to be implemented based on TanStack DB API');
    
  } catch (error) {
    console.error('❌ Error resetting test data:', error);
    throw error;
  }
}

// Test authentication flow
export async function testAuthFlow() {
  try {
    console.log('🔐 Testing authentication flow...');
    
    const { loginUser } = await import('./auth');
    
    // Test login with admin user
    const loginResult = await loginUser({
      email: '<EMAIL>',
      password: 'Admin123!',
    });
    
    if (loginResult.success) {
      console.log('✅ Admin login successful');
      console.log('Token:', loginResult.token?.substring(0, 20) + '...');
    } else {
      console.log('❌ Admin login failed:', loginResult.error);
    }
    
    // Test login with regular user
    const userLoginResult = await loginUser({
      email: '<EMAIL>',
      password: 'User123!',
    });
    
    if (userLoginResult.success) {
      console.log('✅ User login successful');
    } else {
      console.log('❌ User login failed:', userLoginResult.error);
    }
    
  } catch (error) {
    console.error('❌ Error testing auth flow:', error);
    throw error;
  }
}