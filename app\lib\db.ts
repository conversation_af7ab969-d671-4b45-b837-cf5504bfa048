// Database utilities and configuration
import { createId } from "@paralleldrive/cuid2";

// Generate unique IDs using CUID2
export function generateId(): string {
  return createId();
}

// Database configuration and initialization
export const dbConfig = {
  name: "music-judging-app",
  version: 1,
};

// Initialize database collections
export async function initializeDatabase() {
  try {
    console.log("Initializing database...");
    // Database initialization logic will go here
    console.log("✓ Database initialized");
  } catch (error) {
    console.error("Error initializing database:", error);
    throw error;
  }
}