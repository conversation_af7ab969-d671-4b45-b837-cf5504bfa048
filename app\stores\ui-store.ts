// UI state management with Zustand
import { create } from "zustand";
import { persist } from "zustand/middleware";

// Modal types
export type ModalType = 
  | "login" 
  | "register" 
  | "payment" 
  | "audioPlayer" 
  | "confirmDialog"
  | "userProfile"
  | "judgeAssignment";

// UI state interface
interface UIState {
  // Modal states
  modals: {
    [K in ModalType]: boolean;
  };
  
  // Navigation
  sidebarCollapsed: boolean;
  currentWizardStep: number;
  maxWizardStep: number;
  
  // Loading states
  isUploading: boolean;
  uploadProgress: number;
  isProcessingPayment: boolean;
  
  // Audio player state
  audioPlayer: {
    isPlaying: boolean;
    currentTrack: string | null;
    currentTime: number;
    duration: number;
    volume: number;
    playbackRate: number;
  };
  
  // Notification state
  notifications: Array<{
    id: string;
    type: "success" | "error" | "warning" | "info";
    title: string;
    message: string;
    duration?: number;
  }>;
  
  // Confirm dialog state
  confirmDialog: {
    isOpen: boolean;
    title: string;
    message: string;
    confirmText: string;
    cancelText: string;
    onConfirm: (() => void) | null;
    onCancel: (() => void) | null;
    variant: "default" | "destructive";
  };
}

// UI actions interface
interface UIActions {
  // Modal actions
  openModal: (modal: ModalType) => void;
  closeModal: (modal: ModalType) => void;
  closeAllModals: () => void;
  
  // Navigation actions
  toggleSidebar: () => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  setWizardStep: (step: number) => void;
  nextWizardStep: () => void;
  prevWizardStep: () => void;
  resetWizard: () => void;
  
  // Loading actions
  setUploading: (uploading: boolean) => void;
  setUploadProgress: (progress: number) => void;
  setProcessingPayment: (processing: boolean) => void;
  
  // Audio player actions
  setAudioPlaying: (playing: boolean) => void;
  setCurrentTrack: (track: string | null) => void;
  setAudioTime: (time: number) => void;
  setAudioDuration: (duration: number) => void;
  setAudioVolume: (volume: number) => void;
  setPlaybackRate: (rate: number) => void;
  resetAudioPlayer: () => void;
  
  // Notification actions
  addNotification: (notification: Omit<UIState["notifications"][0], "id">) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  
  // Confirm dialog actions
  showConfirmDialog: (config: {
    title: string;
    message: string;
    confirmText?: string;
    cancelText?: string;
    onConfirm: () => void;
    onCancel?: () => void;
    variant?: "default" | "destructive";
  }) => void;
  hideConfirmDialog: () => void;
}

// Initial state
const initialState: UIState = {
  modals: {
    login: false,
    register: false,
    payment: false,
    audioPlayer: false,
    confirmDialog: false,
    userProfile: false,
    judgeAssignment: false,
  },
  sidebarCollapsed: false,
  currentWizardStep: 1,
  maxWizardStep: 6,
  isUploading: false,
  uploadProgress: 0,
  isProcessingPayment: false,
  audioPlayer: {
    isPlaying: false,
    currentTrack: null,
    currentTime: 0,
    duration: 0,
    volume: 1,
    playbackRate: 1,
  },
  notifications: [],
  confirmDialog: {
    isOpen: false,
    title: "",
    message: "",
    confirmText: "Confirm",
    cancelText: "Cancel",
    onConfirm: null,
    onCancel: null,
    variant: "default",
  },
};

// Create the store
export const useUIStore = create<UIState & UIActions>()(
  persist(
    (set, get) => ({
      ...initialState,
      
      // Modal actions
      openModal: (modal) =>
        set((state) => ({
          modals: { ...state.modals, [modal]: true },
        })),
      
      closeModal: (modal) =>
        set((state) => ({
          modals: { ...state.modals, [modal]: false },
        })),
      
      closeAllModals: () =>
        set((state) => ({
          modals: Object.keys(state.modals).reduce(
            (acc, key) => ({ ...acc, [key]: false }),
            {} as UIState["modals"]
          ),
        })),
      
      // Navigation actions
      toggleSidebar: () =>
        set((state) => ({
          sidebarCollapsed: !state.sidebarCollapsed,
        })),
      
      setSidebarCollapsed: (collapsed) =>
        set({ sidebarCollapsed: collapsed }),
      
      setWizardStep: (step) =>
        set({ currentWizardStep: Math.max(1, Math.min(step, get().maxWizardStep)) }),
      
      nextWizardStep: () => {
        const { currentWizardStep, maxWizardStep } = get();
        if (currentWizardStep < maxWizardStep) {
          set({ currentWizardStep: currentWizardStep + 1 });
        }
      },
      
      prevWizardStep: () => {
        const { currentWizardStep } = get();
        if (currentWizardStep > 1) {
          set({ currentWizardStep: currentWizardStep - 1 });
        }
      },
      
      resetWizard: () =>
        set({ currentWizardStep: 1 }),
      
      // Loading actions
      setUploading: (uploading) =>
        set({ isUploading: uploading }),
      
      setUploadProgress: (progress) =>
        set({ uploadProgress: Math.max(0, Math.min(100, progress)) }),
      
      setProcessingPayment: (processing) =>
        set({ isProcessingPayment: processing }),
      
      // Audio player actions
      setAudioPlaying: (playing) =>
        set((state) => ({
          audioPlayer: { ...state.audioPlayer, isPlaying: playing },
        })),
      
      setCurrentTrack: (track) =>
        set((state) => ({
          audioPlayer: { ...state.audioPlayer, currentTrack: track },
        })),
      
      setAudioTime: (time) =>
        set((state) => ({
          audioPlayer: { ...state.audioPlayer, currentTime: time },
        })),
      
      setAudioDuration: (duration) =>
        set((state) => ({
          audioPlayer: { ...state.audioPlayer, duration },
        })),
      
      setAudioVolume: (volume) =>
        set((state) => ({
          audioPlayer: { ...state.audioPlayer, volume: Math.max(0, Math.min(1, volume)) },
        })),
      
      setPlaybackRate: (rate) =>
        set((state) => ({
          audioPlayer: { ...state.audioPlayer, playbackRate: Math.max(0.25, Math.min(2, rate)) },
        })),
      
      resetAudioPlayer: () =>
        set((state) => ({
          audioPlayer: {
            ...initialState.audioPlayer,
            volume: state.audioPlayer.volume, // Preserve volume setting
          },
        })),
      
      // Notification actions
      addNotification: (notification) => {
        const id = Math.random().toString(36).substring(2, 15);
        set((state) => ({
          notifications: [...state.notifications, { ...notification, id }],
        }));
        
        // Auto-remove notification after duration
        const duration = notification.duration || 5000;
        setTimeout(() => {
          get().removeNotification(id);
        }, duration);
      },
      
      removeNotification: (id) =>
        set((state) => ({
          notifications: state.notifications.filter((n) => n.id !== id),
        })),
      
      clearNotifications: () =>
        set({ notifications: [] }),
      
      // Confirm dialog actions
      showConfirmDialog: (config) =>
        set({
          confirmDialog: {
            isOpen: true,
            title: config.title,
            message: config.message,
            confirmText: config.confirmText || "Confirm",
            cancelText: config.cancelText || "Cancel",
            onConfirm: config.onConfirm,
            onCancel: config.onCancel || null,
            variant: config.variant || "default",
          },
        }),
      
      hideConfirmDialog: () =>
        set({
          confirmDialog: {
            ...initialState.confirmDialog,
          },
        }),
    }),
    {
      name: "ui-store",
      // Only persist certain UI preferences
      partialize: (state) => ({
        sidebarCollapsed: state.sidebarCollapsed,
        audioPlayer: {
          volume: state.audioPlayer.volume,
          playbackRate: state.audioPlayer.playbackRate,
        },
      }),
    }
  )
);

// Selector hooks for better performance
export const useModals = () => useUIStore((state) => state.modals);
export const useAudioPlayer = () => useUIStore((state) => state.audioPlayer);
export const useNotifications = () => useUIStore((state) => state.notifications);
export const useConfirmDialog = () => useUIStore((state) => state.confirmDialog);
export const useWizardState = () => useUIStore((state) => ({
  currentStep: state.currentWizardStep,
  maxStep: state.maxWizardStep,
}));
export const useLoadingState = () => useUIStore((state) => ({
  isUploading: state.isUploading,
  uploadProgress: state.uploadProgress,
  isProcessingPayment: state.isProcessingPayment,
}));