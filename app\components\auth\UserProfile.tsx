import React, { useState } from "react";
import { useForm } from "@tanstack/react-form";
import { User, Mail, Calendar, Shield, Loader2, Save } from "lucide-react";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { useAuth } from "../../lib/auth-context";
import { usersCollection } from "../../lib/collections";
import { validateEmail } from "../../lib/auth";
import { format } from "date-fns";

interface UserProfileData {
  firstName: string;
  lastName: string;
  email: string;
}

export function UserProfile() {
  const { user, refreshAuth } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const form = useForm<UserProfileData>({
    defaultValues: {
      firstName: user?.firstName || "",
      lastName: user?.lastName || "",
      email: user?.email || "",
    },
    onSubmit: async ({ value }) => {
      if (!user) return;

      setIsLoading(true);
      setError(null);
      setSuccess(null);

      try {
        // Check if email is being changed and if it's already taken
        if (value.email !== user.email) {
          const existingUser = await usersCollection.findFirst({
            where: (u) => u.email === value.email.toLowerCase().trim() && u.id !== user.id
          });

          if (existingUser) {
            setError("This email address is already in use");
            return;
          }
        }

        // Update user profile
        const updatedUser = {
          ...user,
          firstName: value.firstName.trim(),
          lastName: value.lastName.trim(),
          email: value.email.toLowerCase().trim(),
          updatedAt: new Date(),
        };

        await usersCollection.update(user.id, updatedUser);
        
        // Refresh auth context to get updated user data
        await refreshAuth();
        
        setSuccess("Profile updated successfully");
      } catch (err) {
        console.error("Profile update error:", err);
        setError("Failed to update profile. Please try again.");
      } finally {
        setIsLoading(false);
      }
    },
  });

  if (!user) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <User className="mx-auto h-12 w-12 text-gray-400" />
          <p className="mt-2 text-sm text-gray-600">No user data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Profile Information
          </CardTitle>
          <CardDescription>
            Update your personal information and email address
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form
            onSubmit={(e) => {
              e.preventDefault();
              e.stopPropagation();
              form.handleSubmit();
            }}
            className="space-y-4"
          >
            {error && (
              <div className="rounded-md bg-red-50 p-4">
                <div className="text-sm text-red-700">{error}</div>
              </div>
            )}

            {success && (
              <div className="rounded-md bg-green-50 p-4">
                <div className="text-sm text-green-700">{success}</div>
              </div>
            )}

            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <form.Field
                name="firstName"
                validators={{
                  onChange: ({ value }) => {
                    if (!value?.trim()) return "First name is required";
                    if (value.trim().length < 2) return "First name must be at least 2 characters";
                    return undefined;
                  },
                }}
              >
                {(field) => (
                  <div className="space-y-2">
                    <label htmlFor="firstName" className="text-sm font-medium text-gray-700">
                      First name
                    </label>
                    <Input
                      id="firstName"
                      type="text"
                      value={field.state.value}
                      onChange={(e) => field.handleChange(e.target.value)}
                      onBlur={field.handleBlur}
                      className={field.state.meta.errors.length > 0 ? "border-red-500" : ""}
                    />
                    {field.state.meta.errors.length > 0 && (
                      <p className="text-sm text-red-600">{field.state.meta.errors[0]}</p>
                    )}
                  </div>
                )}
              </form.Field>

              <form.Field
                name="lastName"
                validators={{
                  onChange: ({ value }) => {
                    if (!value?.trim()) return "Last name is required";
                    if (value.trim().length < 2) return "Last name must be at least 2 characters";
                    return undefined;
                  },
                }}
              >
                {(field) => (
                  <div className="space-y-2">
                    <label htmlFor="lastName" className="text-sm font-medium text-gray-700">
                      Last name
                    </label>
                    <Input
                      id="lastName"
                      type="text"
                      value={field.state.value}
                      onChange={(e) => field.handleChange(e.target.value)}
                      onBlur={field.handleBlur}
                      className={field.state.meta.errors.length > 0 ? "border-red-500" : ""}
                    />
                    {field.state.meta.errors.length > 0 && (
                      <p className="text-sm text-red-600">{field.state.meta.errors[0]}</p>
                    )}
                  </div>
                )}
              </form.Field>
            </div>

            <form.Field
              name="email"
              validators={{
                onChange: ({ value }) => {
                  if (!value) return "Email is required";
                  if (!validateEmail(value)) return "Please enter a valid email address";
                  return undefined;
                },
              }}
            >
              {(field) => (
                <div className="space-y-2">
                  <label htmlFor="email" className="text-sm font-medium text-gray-700">
                    Email address
                  </label>
                  <Input
                    id="email"
                    type="email"
                    value={field.state.value}
                    onChange={(e) => field.handleChange(e.target.value)}
                    onBlur={field.handleBlur}
                    className={field.state.meta.errors.length > 0 ? "border-red-500" : ""}
                  />
                  {field.state.meta.errors.length > 0 && (
                    <p className="text-sm text-red-600">{field.state.meta.errors[0]}</p>
                  )}
                </div>
              )}
            </form.Field>

            <div className="flex justify-end">
              <Button
                type="submit"
                disabled={isLoading || !form.state.canSubmit}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save changes
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Account Information
          </CardTitle>
          <CardDescription>
            View your account details and status
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Account Type</label>
              <div className="flex items-center gap-2">
                {user.isAdmin ? (
                  <>
                    <Shield className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-600">Administrator</span>
                  </>
                ) : (
                  <>
                    <User className="h-4 w-4 text-gray-600" />
                    <span className="text-sm text-gray-600">Participant</span>
                  </>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Member Since</label>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-600">
                  {format(new Date(user.createdAt), "MMMM d, yyyy")}
                </span>
              </div>
            </div>

            {user.lastLoginAt && (
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Last Login</label>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-600">
                    {format(new Date(user.lastLoginAt), "MMMM d, yyyy 'at' h:mm a")}
                  </span>
                </div>
              </div>
            )}

            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Total Entries</label>
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-600">
                  {user.groupIds?.length || 0} entries
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}