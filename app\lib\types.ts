// Re-export all types and utilities for easy importing
export * from "./schema";
export * from "./collections";
export { db, generateId } from "./db";
export { initializeDatabase, runMigrations, rollbackToVersion } from "./migrations";
export { seedDatabase } from "./seed";

// Additional utility types
export type ContestType = "MARK_OF_EXCELLENCE" | "CITATION_OF_EXCELLENCE";
export type GroupStatus = "DRAFT" | "SUBMITTED" | "PAID" | "JUDGED" | "COMPLETED";
export type InvoiceStatus = "DRAFT" | "SENT" | "PAID" | "FAILED" | "CANCELLED";
export type JudgePhase = "NO_PHASE" | "PHASE_1" | "PHASE_2";

// Utility type for creating new records (without id, createdAt, updatedAt)
export type CreateRecord<T> = Omit<T, "id" | "createdAt" | "updatedAt">;

// Utility type for updating records (partial, without id, createdAt, updatedAt)
export type UpdateRecord<T> = Partial<Omit<T, "id" | "createdAt" | "updatedAt">>;

// Invoice line item type
export interface InvoiceLineItem {
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

// Outstanding soloist type
export interface OutstandingSoloist {
  soloistId: string;
  instrument: string;
  timestamp?: number;
}

// Judge scores type (flexible scoring system)
export type JudgeScores = Record<string, number>;