// Authentication utilities and JWT management
import bcrypt from "bcryptjs";
import { SignJWT, jwtVerify } from "jose";
import { generateId } from "./db";
import { usersCollection } from "./collections";
import type { User } from "./schema";

// JWT configuration
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || "your-secret-key-change-in-production-make-it-at-least-32-characters-long"
);
const JWT_EXPIRES_IN = "7d";
const BCRYPT_ROUNDS = 12;

// JWT payload interface
export interface JWTPayload {
  userId: string;
  email: string;
  isAdmin: boolean;
  iat?: number;
  exp?: number;
}

// Authentication result types
export interface AuthResult {
  success: boolean;
  user?: User;
  token?: string;
  error?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

// Password hashing utilities
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, BCRYPT_ROUNDS);
}

export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash);
}

// JWT utilities
export async function generateToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): Promise<string> {
  const jwt = await new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('7d')
    .sign(JWT_SECRET);
  
  return jwt;
}

export async function verifyToken(token: string): Promise<JWTPayload | null> {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    return payload as JWTPayload;
  } catch (error) {
    console.error("JWT verification error:", error);
    return null;
  }
}

// Authentication functions
export async function registerUser(data: RegisterData): Promise<AuthResult> {
  try {
    // Check if user already exists
    const existingUser = await usersCollection.findFirst({
      where: (user) => user.email === data.email
    });

    if (existingUser) {
      return {
        success: false,
        error: "User with this email already exists"
      };
    }

    // Hash password
    const passwordHash = await hashPassword(data.password);

    // Create new user
    const newUser: User = {
      id: generateId(),
      email: data.email.toLowerCase().trim(),
      passwordHash,
      firstName: data.firstName.trim(),
      lastName: data.lastName.trim(),
      isAdmin: false,
      groupIds: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Insert user into collection
    await usersCollection.insert(newUser);

    // Generate token
    const token = await generateToken({
      userId: newUser.id,
      email: newUser.email,
      isAdmin: newUser.isAdmin,
    });

    // Return user without password hash
    const { passwordHash: _, ...userWithoutPassword } = newUser;

    return {
      success: true,
      user: userWithoutPassword as User,
      token,
    };
  } catch (error) {
    console.error("Registration error:", error);
    return {
      success: false,
      error: "Registration failed. Please try again.",
    };
  }
}

export async function loginUser(credentials: LoginCredentials): Promise<AuthResult> {
  try {
    // Find user by email
    const user = await usersCollection.findFirst({
      where: (u) => u.email === credentials.email.toLowerCase().trim()
    });

    if (!user) {
      return {
        success: false,
        error: "Invalid email or password",
      };
    }

    // Verify password
    const isValidPassword = await verifyPassword(credentials.password, user.passwordHash);

    if (!isValidPassword) {
      return {
        success: false,
        error: "Invalid email or password",
      };
    }

    // Update last login time
    const updatedUser = {
      ...user,
      lastLoginAt: new Date(),
      updatedAt: new Date(),
    };

    await usersCollection.update(user.id, updatedUser);

    // Generate token
    const token = await generateToken({
      userId: user.id,
      email: user.email,
      isAdmin: user.isAdmin,
    });

    // Return user without password hash
    const { passwordHash: _, ...userWithoutPassword } = updatedUser;

    return {
      success: true,
      user: userWithoutPassword as User,
      token,
    };
  } catch (error) {
    console.error("Login error:", error);
    return {
      success: false,
      error: "Login failed. Please try again.",
    };
  }
}

export async function getUserFromToken(token: string): Promise<User | null> {
  try {
    console.log("Verifying token...");
    const payload = await verifyToken(token);
    console.log("Token payload:", !!payload);
    if (!payload) return null;

    console.log("Finding user with ID:", payload.userId);
    const user = await usersCollection.findFirst({
      where: (u) => u.id === payload.userId
    });
    console.log("User found:", !!user);

    if (!user) return null;

    // Return user without password hash
    const { passwordHash: _, ...userWithoutPassword } = user;
    return userWithoutPassword as User;
  } catch (error) {
    console.error("Token verification error:", error);
    return null;
  }
}

// Password reset utilities
export async function generatePasswordResetToken(email: string): Promise<string | null> {
  try {
    const user = await usersCollection.findFirst({
      where: (u) => u.email === email.toLowerCase().trim()
    });

    if (!user) return null;

    // Generate a short-lived reset token (1 hour)
    const resetToken = await new SignJWT({ 
      userId: user.id, 
      email: user.email, 
      type: "password-reset" 
    })
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setExpirationTime('1h')
      .sign(JWT_SECRET);

    return resetToken;
  } catch (error) {
    console.error("Password reset token generation error:", error);
    return null;
  }
}

export async function resetPassword(token: string, newPassword: string): Promise<AuthResult> {
  try {
    // Verify reset token
    const { payload } = await jwtVerify(token, JWT_SECRET);
    
    if (payload.type !== "password-reset") {
      return {
        success: false,
        error: "Invalid reset token",
      };
    }

    // Find user
    const user = await usersCollection.findFirst({
      where: (u) => u.id === payload.userId
    });

    if (!user) {
      return {
        success: false,
        error: "User not found",
      };
    }

    // Hash new password
    const passwordHash = await hashPassword(newPassword);

    // Update user password
    const updatedUser = {
      ...user,
      passwordHash,
      updatedAt: new Date(),
    };

    await usersCollection.update(user.id, updatedUser);

    return {
      success: true,
    };
  } catch (error) {
    console.error("Password reset error:", error);
    return {
      success: false,
      error: "Password reset failed. Token may be expired.",
    };
  }
}

// Validation utilities
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function validatePassword(password: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (password.length < 8) {
    errors.push("Password must be at least 8 characters long");
  }

  if (!/[A-Z]/.test(password)) {
    errors.push("Password must contain at least one uppercase letter");
  }

  if (!/[a-z]/.test(password)) {
    errors.push("Password must contain at least one lowercase letter");
  }

  if (!/\d/.test(password)) {
    errors.push("Password must contain at least one number");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}