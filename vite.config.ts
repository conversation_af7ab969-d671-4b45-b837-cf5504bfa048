import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tsconfigPaths from 'vite-tsconfig-paths'
import { tanstackStart } from '@tanstack/react-start/plugin/vite'
import { tanstackRouter } from '@tanstack/router-vite-plugin'

export default defineConfig({
  server: {
    port: 3000,
  },
  plugins: [
    tanstackRouter(),
    react(),
    tsconfigPaths(),
    tanstackStart({
      customViteReactPlugin: true
    })
  ],
})