// Password reset functionality
import { generatePasswordResetToken, resetPassword } from "./auth";

// Email service interface (to be implemented with Send<PERSON>rid later)
interface EmailService {
  sendPasswordResetEmail(email: string, resetToken: string): Promise<boolean>;
}

// Mock email service for now
class MockEmailService implements EmailService {
  async sendPasswordResetEmail(email: string, resetToken: string): Promise<boolean> {
    // In development, log the reset link to console
    if (process.env.NODE_ENV === "development") {
      const resetUrl = `${window.location.origin}/reset-password?token=${resetToken}`;
      console.log(`Password reset email for ${email}:`);
      console.log(`Reset URL: ${resetUrl}`);
      console.log(`Token: ${resetToken}`);
    }
    
    // TODO: Implement actual email sending with SendGrid
    return true;
  }
}

// Email service instance
const emailService = new MockEmailService();

// Request password reset
export async function requestPasswordReset(email: string): Promise<{
  success: boolean;
  message: string;
}> {
  try {
    const resetToken = await generatePasswordResetToken(email);
    
    if (!resetToken) {
      // Don't reveal whether email exists or not for security
      return {
        success: true,
        message: "If an account with that email exists, a password reset link has been sent.",
      };
    }

    const emailSent = await emailService.sendPasswordResetEmail(email, resetToken);
    
    if (!emailSent) {
      return {
        success: false,
        message: "Failed to send password reset email. Please try again.",
      };
    }

    return {
      success: true,
      message: "If an account with that email exists, a password reset link has been sent.",
    };
  } catch (error) {
    console.error("Password reset request error:", error);
    return {
      success: false,
      message: "An error occurred. Please try again.",
    };
  }
}

// Complete password reset
export async function completePasswordReset(token: string, newPassword: string): Promise<{
  success: boolean;
  message: string;
}> {
  try {
    const result = await resetPassword(token, newPassword);
    
    if (result.success) {
      return {
        success: true,
        message: "Password has been reset successfully. You can now log in with your new password.",
      };
    } else {
      return {
        success: false,
        message: result.error || "Password reset failed. The link may be expired or invalid.",
      };
    }
  } catch (error) {
    console.error("Password reset completion error:", error);
    return {
      success: false,
      message: "An error occurred. Please try again.",
    };
  }
}

// Validate reset token
export function validateResetToken(token: string): boolean {
  try {
    // This will be implemented in the auth module
    // For now, just check if token exists and is not empty
    return !!token && token.length > 0;
  } catch (error) {
    return false;
  }
}

// Generate password reset email template
export function generatePasswordResetEmailTemplate(resetUrl: string): {
  subject: string;
  html: string;
  text: string;
} {
  const subject = "Reset Your The Foundation for Music Education Password";
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Password Reset</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #f8f9fa; padding: 20px; text-align: center; }
        .content { padding: 20px; }
        .button { 
          display: inline-block; 
          background-color: #007bff; 
          color: white; 
          padding: 12px 24px; 
          text-decoration: none; 
          border-radius: 4px; 
          margin: 20px 0; 
        }
        .footer { background-color: #f8f9fa; padding: 20px; font-size: 12px; color: #666; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>The Foundation for Music Education</h1>
        </div>
        <div class="content">
          <h2>Password Reset Request</h2>
          <p>You have requested to reset your password for your The Foundation for Music Education account.</p>
          <p>Click the button below to reset your password:</p>
          <a href="${resetUrl}" class="button">Reset Password</a>
          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p><a href="${resetUrl}">${resetUrl}</a></p>
          <p><strong>This link will expire in 1 hour.</strong></p>
          <p>If you didn't request this password reset, you can safely ignore this email.</p>
        </div>
        <div class="footer">
          <p>This is an automated email from The Foundation for Music Education. Please do not reply to this email.</p>
        </div>
      </div>
    </body>
    </html>
  `;
  
  const text = `
    The Foundation for Music Education - Password Reset Request
    
    You have requested to reset your password for your The Foundation for Music Education account.
    
    Click the link below to reset your password:
    ${resetUrl}
    
    This link will expire in 1 hour.
    
    If you didn't request this password reset, you can safely ignore this email.
    
    This is an automated email from The Foundation for Music Education. Please do not reply to this email.
  `;

  return { subject, html, text };
}