// Database schema definitions for TanStack DB
import { z } from "zod";

// User Schema
export const userSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  passwordHash: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  isAdmin: z.boolean().default(false),
  groupIds: z.array(z.string()).default([]),
  createdAt: z.date(),
  updatedAt: z.date(),
  lastLoginAt: z.date().optional(),
});

// Contest Schema
export const contestSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.enum(["MARK_OF_EXCELLENCE", "CITATION_OF_EXCELLENCE"]),
  isActive: z.boolean().default(true),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Category Schema
export const categorySchema = z.object({
  id: z.string(),
  contestId: z.string(),
  name: z.string(),
  basePrice: z.number(),
  supportsSoloists: z.boolean().default(false),
  maxSoloists: z.number().optional(),
  isActive: z.boolean().default(true),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Classification Schema
export const classificationSchema = z.object({
  id: z.string(),
  categoryId: z.string(),
  name: z.string(),
  price: z.number(),
  isActive: z.boolean().default(true),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Group Schema
export const groupSchema = z.object({
  id: z.string(),
  userId: z.string(),
  ensembleName: z.string(),
  schoolName: z.string(),
  enrollment: z.number(),
  contestId: z.string(),
  categoryId: z.string(),
  classificationIds: z.array(z.string()),
  maskedName: z.string().optional(),
  status: z.enum(["DRAFT", "SUBMITTED", "PAID", "JUDGED", "COMPLETED"]).default("DRAFT"),
  submittedAt: z.date().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Director Schema
export const directorSchema = z.object({
  id: z.string(),
  groupId: z.string(),
  prefix: z.string().optional(),
  firstName: z.string(),
  lastName: z.string(),
  isPrimary: z.boolean().default(false),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Contact Info Schema
export const contactInfoSchema = z.object({
  id: z.string(),
  groupId: z.string(),
  phone: z.string(),
  address: z.string(),
  city: z.string(),
  state: z.string(),
  zipCode: z.string(),
  shippingAddress: z.string().optional(),
  shippingCity: z.string().optional(),
  shippingState: z.string().optional(),
  shippingZipCode: z.string().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Selection Schema
export const selectionSchema = z.object({
  id: z.string(),
  groupId: z.string(),
  title: z.string(),
  composer: z.string(),
  arranger: z.string().optional(),
  duration: z.string(),
  recordingDate: z.date(),
  venue: z.string(),
  audioFileUrl: z.string().optional(),
  audioFileName: z.string().optional(),
  audioFileSize: z.number().optional(),
  order: z.number(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Soloist Schema
export const soloistSchema = z.object({
  id: z.string(),
  selectionId: z.string(),
  name: z.string(),
  instrument: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Invoice Schema
export const invoiceSchema = z.object({
  id: z.string(),
  groupId: z.string(),
  invoiceNumber: z.string(),
  subtotal: z.number(),
  tax: z.number().default(0),
  total: z.number(),
  lineItems: z.array(z.object({
    description: z.string(),
    quantity: z.number(),
    unitPrice: z.number(),
    total: z.number(),
  })),
  status: z.enum(["DRAFT", "SENT", "PAID", "FAILED", "CANCELLED"]).default("DRAFT"),
  stripePaymentIntentId: z.string().optional(),
  stripeInvoiceId: z.string().optional(),
  paymentMethod: z.string().optional(),
  paidAt: z.date().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Judge Schema
export const judgeSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string().email(),
  accessToken: z.string(),
  introVideoUrl: z.string().optional(),
  isActive: z.boolean().default(true),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Judge Assignment Schema
export const judgeAssignmentSchema = z.object({
  id: z.string(),
  judgeId: z.string(),
  contestId: z.string(),
  categoryId: z.string(),
  classificationId: z.string(),
  phase: z.enum(["NO_PHASE", "PHASE_1", "PHASE_2"]).default("NO_PHASE"),
  groupIds: z.array(z.string()).default([]),
  isActive: z.boolean().default(true),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Judge Evaluation Schema
export const judgeEvaluationSchema = z.object({
  id: z.string(),
  judgeId: z.string(),
  groupId: z.string(),
  assignmentId: z.string(),
  writtenComments: z.string().optional(),
  scores: z.record(z.string(), z.number()).optional(),
  movesOn: z.boolean().optional(),
  nationalCitation: z.boolean().optional(),
  commended: z.boolean().optional(),
  outstandingSoloists: z.array(z.object({
    soloistId: z.string(),
    instrument: z.string(),
    timestamp: z.number().optional(),
  })).default([]),
  isComplete: z.boolean().default(false),
  completedAt: z.date().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Voice Memo Schema
export const voiceMemoSchema = z.object({
  id: z.string(),
  evaluationId: z.string(),
  selectionId: z.string().optional(),
  audioUrl: z.string(),
  duration: z.number(),
  timestamp: z.number().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Download Code Schema
export const downloadCodeSchema = z.object({
  id: z.string(),
  groupId: z.string(),
  code: z.string(),
  isActive: z.boolean().default(true),
  expiresAt: z.date().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Competition State Schema
export const competitionStateSchema = z.object({
  id: z.string(),
  year: z.number(),
  registrationOpen: z.boolean().default(false),
  resultsLive: z.boolean().default(false),
  resultsMessage: z.string().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Export all schemas
export {
  userSchema,
  contestSchema,
  categorySchema,
  classificationSchema,
  groupSchema,
  directorSchema,
  contactInfoSchema,
  selectionSchema,
  soloistSchema,
  invoiceSchema,
  judgeSchema,
  judgeAssignmentSchema,
  judgeEvaluationSchema,
  voiceMemoSchema,
  downloadCodeSchema,
  competitionStateSchema,
};

// Type exports
export type User = z.infer<typeof userSchema>;
export type Contest = z.infer<typeof contestSchema>;
export type Category = z.infer<typeof categorySchema>;
export type Classification = z.infer<typeof classificationSchema>;
export type Group = z.infer<typeof groupSchema>;
export type Director = z.infer<typeof directorSchema>;
export type ContactInfo = z.infer<typeof contactInfoSchema>;
export type Selection = z.infer<typeof selectionSchema>;
export type Soloist = z.infer<typeof soloistSchema>;
export type Invoice = z.infer<typeof invoiceSchema>;
export type Judge = z.infer<typeof judgeSchema>;
export type JudgeAssignment = z.infer<typeof judgeAssignmentSchema>;
export type JudgeEvaluation = z.infer<typeof judgeEvaluationSchema>;
export type VoiceMemo = z.infer<typeof voiceMemoSchema>;
export type DownloadCode = z.infer<typeof downloadCodeSchema>;
export type CompetitionState = z.infer<typeof competitionStateSchema>;