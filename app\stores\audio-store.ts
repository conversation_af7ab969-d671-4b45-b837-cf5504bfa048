// Audio player state management
import { create } from "zustand";
import { persist } from "zustand/middleware";

// Audio track interface
export interface AudioTrack {
  id: string;
  title: string;
  composer: string;
  duration: number;
  url: string;
  groupId?: string;
  selectionId?: string;
}

// Voice memo interface
export interface VoiceMemo {
  id: string;
  audioUrl: string;
  duration: number;
  timestamp: number;
  createdAt: Date;
  evaluationId?: string;
  selectionId?: string;
}

// Audio player state
interface AudioPlayerState {
  // Current playback
  currentTrack: AudioTrack | null;
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  buffered: number;
  
  // Playlist
  playlist: AudioTrack[];
  currentIndex: number;
  shuffle: boolean;
  repeat: "none" | "one" | "all";
  
  // Audio settings
  volume: number;
  muted: boolean;
  playbackRate: number;
  
  // Voice memos
  voiceMemos: VoiceMemo[];
  isRecording: boolean;
  recordingDuration: number;
  
  // UI state
  showPlayer: boolean;
  showPlaylist: boolean;
  showVoiceMemos: boolean;
  
  // Caching
  cachedTracks: Record<string, string>; // trackId -> blob URL
}

// Audio actions
interface AudioActions {
  // Playback control
  play: () => void;
  pause: () => void;
  stop: () => void;
  seek: (time: number) => void;
  setVolume: (volume: number) => void;
  toggleMute: () => void;
  setPlaybackRate: (rate: number) => void;
  
  // Track management
  loadTrack: (track: AudioTrack) => void;
  setCurrentTime: (time: number) => void;
  setDuration: (duration: number) => void;
  setBuffered: (buffered: number) => void;
  
  // Playlist management
  setPlaylist: (tracks: AudioTrack[]) => void;
  addToPlaylist: (track: AudioTrack) => void;
  removeFromPlaylist: (trackId: string) => void;
  clearPlaylist: () => void;
  nextTrack: () => void;
  previousTrack: () => void;
  jumpToTrack: (index: number) => void;
  toggleShuffle: () => void;
  setRepeat: (repeat: "none" | "one" | "all") => void;
  
  // Voice memo management
  startRecording: () => void;
  stopRecording: () => void;
  pauseRecording: () => void;
  resumeRecording: () => void;
  setRecordingDuration: (duration: number) => void;
  addVoiceMemo: (memo: Omit<VoiceMemo, "id" | "createdAt">) => void;
  removeVoiceMemo: (memoId: string) => void;
  clearVoiceMemos: () => void;
  
  // UI actions
  showPlayer: () => void;
  hidePlayer: () => void;
  togglePlayer: () => void;
  showPlaylist: () => void;
  hidePlaylist: () => void;
  togglePlaylist: () => void;
  showVoiceMemos: () => void;
  hideVoiceMemos: () => void;
  toggleVoiceMemos: () => void;
  
  // Caching
  cacheTrack: (trackId: string, blobUrl: string) => void;
  removeCachedTrack: (trackId: string) => void;
  clearCache: () => void;
  
  // Reset
  reset: () => void;
}

// Initial state
const initialState: AudioPlayerState = {
  currentTrack: null,
  isPlaying: false,
  currentTime: 0,
  duration: 0,
  buffered: 0,
  playlist: [],
  currentIndex: -1,
  shuffle: false,
  repeat: "none",
  volume: 1,
  muted: false,
  playbackRate: 1,
  voiceMemos: [],
  isRecording: false,
  recordingDuration: 0,
  showPlayer: false,
  showPlaylist: false,
  showVoiceMemos: false,
  cachedTracks: {},
};

// Create the store
export const useAudioStore = create<AudioPlayerState & AudioActions>()(
  persist(
    (set, get) => ({
      ...initialState,
      
      // Playback control
      play: () => set({ isPlaying: true }),
      pause: () => set({ isPlaying: false }),
      stop: () => set({ isPlaying: false, currentTime: 0 }),
      
      seek: (time) => {
        const { duration } = get();
        const clampedTime = Math.max(0, Math.min(time, duration));
        set({ currentTime: clampedTime });
      },
      
      setVolume: (volume) => {
        const clampedVolume = Math.max(0, Math.min(1, volume));
        set({ volume: clampedVolume, muted: clampedVolume === 0 });
      },
      
      toggleMute: () => {
        const { muted, volume } = get();
        if (muted) {
          set({ muted: false, volume: volume || 0.5 });
        } else {
          set({ muted: true });
        }
      },
      
      setPlaybackRate: (rate) => {
        const clampedRate = Math.max(0.25, Math.min(2, rate));
        set({ playbackRate: clampedRate });
      },
      
      // Track management
      loadTrack: (track) => {
        const { playlist } = get();
        const index = playlist.findIndex((t) => t.id === track.id);
        set({
          currentTrack: track,
          currentIndex: index,
          currentTime: 0,
          duration: track.duration || 0,
          showPlayer: true,
        });
      },
      
      setCurrentTime: (time) => set({ currentTime: time }),
      setDuration: (duration) => set({ duration }),
      setBuffered: (buffered) => set({ buffered }),
      
      // Playlist management
      setPlaylist: (tracks) => set({ playlist: tracks }),
      
      addToPlaylist: (track) => {
        const { playlist } = get();
        if (!playlist.find((t) => t.id === track.id)) {
          set({ playlist: [...playlist, track] });
        }
      },
      
      removeFromPlaylist: (trackId) => {
        const { playlist, currentIndex } = get();
        const newPlaylist = playlist.filter((t) => t.id !== trackId);
        const removedIndex = playlist.findIndex((t) => t.id === trackId);
        
        let newCurrentIndex = currentIndex;
        if (removedIndex <= currentIndex && currentIndex > 0) {
          newCurrentIndex = currentIndex - 1;
        } else if (removedIndex === currentIndex) {
          newCurrentIndex = Math.min(currentIndex, newPlaylist.length - 1);
        }
        
        set({
          playlist: newPlaylist,
          currentIndex: newCurrentIndex,
        });
      },
      
      clearPlaylist: () => set({ playlist: [], currentIndex: -1 }),
      
      nextTrack: () => {
        const { playlist, currentIndex, shuffle, repeat } = get();
        if (playlist.length === 0) return;
        
        let nextIndex: number;
        
        if (shuffle) {
          nextIndex = Math.floor(Math.random() * playlist.length);
        } else if (repeat === "one") {
          nextIndex = currentIndex;
        } else if (currentIndex >= playlist.length - 1) {
          nextIndex = repeat === "all" ? 0 : currentIndex;
        } else {
          nextIndex = currentIndex + 1;
        }
        
        if (nextIndex !== currentIndex || repeat === "one") {
          const nextTrack = playlist[nextIndex];
          if (nextTrack) {
            get().loadTrack(nextTrack);
          }
        }
      },
      
      previousTrack: () => {
        const { playlist, currentIndex, shuffle } = get();
        if (playlist.length === 0) return;
        
        let prevIndex: number;
        
        if (shuffle) {
          prevIndex = Math.floor(Math.random() * playlist.length);
        } else if (currentIndex <= 0) {
          prevIndex = playlist.length - 1;
        } else {
          prevIndex = currentIndex - 1;
        }
        
        const prevTrack = playlist[prevIndex];
        if (prevTrack) {
          get().loadTrack(prevTrack);
        }
      },
      
      jumpToTrack: (index) => {
        const { playlist } = get();
        const track = playlist[index];
        if (track) {
          get().loadTrack(track);
        }
      },
      
      toggleShuffle: () => set((state) => ({ shuffle: !state.shuffle })),
      
      setRepeat: (repeat) => set({ repeat }),
      
      // Voice memo management
      startRecording: () => set({ isRecording: true, recordingDuration: 0 }),
      stopRecording: () => set({ isRecording: false, recordingDuration: 0 }),
      pauseRecording: () => set({ isRecording: false }),
      resumeRecording: () => set({ isRecording: true }),
      setRecordingDuration: (duration) => set({ recordingDuration: duration }),
      
      addVoiceMemo: (memo) => {
        const id = Math.random().toString(36).substring(2, 15);
        const newMemo: VoiceMemo = {
          ...memo,
          id,
          createdAt: new Date(),
        };
        set((state) => ({
          voiceMemos: [...state.voiceMemos, newMemo],
        }));
      },
      
      removeVoiceMemo: (memoId) =>
        set((state) => ({
          voiceMemos: state.voiceMemos.filter((memo) => memo.id !== memoId),
        })),
      
      clearVoiceMemos: () => set({ voiceMemos: [] }),
      
      // UI actions
      showPlayer: () => set({ showPlayer: true }),
      hidePlayer: () => set({ showPlayer: false }),
      togglePlayer: () => set((state) => ({ showPlayer: !state.showPlayer })),
      showPlaylist: () => set({ showPlaylist: true }),
      hidePlaylist: () => set({ showPlaylist: false }),
      togglePlaylist: () => set((state) => ({ showPlaylist: !state.showPlaylist })),
      showVoiceMemos: () => set({ showVoiceMemos: true }),
      hideVoiceMemos: () => set({ showVoiceMemos: false }),
      toggleVoiceMemos: () => set((state) => ({ showVoiceMemos: !state.showVoiceMemos })),
      
      // Caching
      cacheTrack: (trackId, blobUrl) =>
        set((state) => ({
          cachedTracks: { ...state.cachedTracks, [trackId]: blobUrl },
        })),
      
      removeCachedTrack: (trackId) =>
        set((state) => {
          const { [trackId]: removed, ...rest } = state.cachedTracks;
          return { cachedTracks: rest };
        }),
      
      clearCache: () => {
        const { cachedTracks } = get();
        // Revoke blob URLs to free memory
        Object.values(cachedTracks).forEach((blobUrl) => {
          URL.revokeObjectURL(blobUrl);
        });
        set({ cachedTracks: {} });
      },
      
      // Reset
      reset: () => {
        const { cachedTracks } = get();
        // Revoke blob URLs before resetting
        Object.values(cachedTracks).forEach((blobUrl) => {
          URL.revokeObjectURL(blobUrl);
        });
        set(initialState);
      },
    }),
    {
      name: "audio-store",
      // Only persist user preferences, not playback state
      partialize: (state) => ({
        volume: state.volume,
        muted: state.muted,
        playbackRate: state.playbackRate,
        shuffle: state.shuffle,
        repeat: state.repeat,
      }),
    }
  )
);

// Selector hooks
export const useCurrentTrack = () => useAudioStore((state) => state.currentTrack);
export const usePlaybackState = () => useAudioStore((state) => ({
  isPlaying: state.isPlaying,
  currentTime: state.currentTime,
  duration: state.duration,
  buffered: state.buffered,
}));
export const useAudioSettings = () => useAudioStore((state) => ({
  volume: state.volume,
  muted: state.muted,
  playbackRate: state.playbackRate,
}));
export const usePlaylist = () => useAudioStore((state) => ({
  playlist: state.playlist,
  currentIndex: state.currentIndex,
  shuffle: state.shuffle,
  repeat: state.repeat,
}));
export const useVoiceMemos = () => useAudioStore((state) => state.voiceMemos);
export const useRecordingState = () => useAudioStore((state) => ({
  isRecording: state.isRecording,
  recordingDuration: state.recordingDuration,
}));