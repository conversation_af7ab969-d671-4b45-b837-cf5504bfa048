// Database seeding and initialization
import { generateId } from "./db";
import {
  contestsCollection,
  categoriesCollection,
  classificationsCollection,
  competitionStateCollection,
} from "./collections";

// Contest data based on design document
const contestsData = [
  {
    id: generateId(),
    name: "Mark of Excellence",
    type: "MARK_OF_EXCELLENCE" as const,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: generateId(),
    name: "Citation of Excellence",
    type: "CITATION_OF_EXCELLENCE" as const,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

// Category data with pricing
const categoriesData = [
  // Mark of Excellence Categories
  {
    id: generateId(),
    contestId: contestsData[0].id,
    name: "Concert Band",
    basePrice: 75,
    supportsSoloists: true,
    maxSoloists: 5,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: generateId(),
    contestId: contestsData[0].id,
    name: "Symphonic Band",
    basePrice: 75,
    supportsSoloists: true,
    maxSoloists: 5,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: generateId(),
    contestId: contestsData[0].id,
    name: "Wind Ensemble",
    basePrice: 75,
    supportsSoloists: true,
    maxSoloists: 5,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: generateId(),
    contestId: contestsData[0].id,
    name: "Jazz Ensemble",
    basePrice: 75,
    supportsSoloists: true,
    maxSoloists: 5,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: generateId(),
    contestId: contestsData[0].id,
    name: "Orchestra",
    basePrice: 75,
    supportsSoloists: true,
    maxSoloists: 5,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: generateId(),
    contestId: contestsData[0].id,
    name: "Choir",
    basePrice: 75,
    supportsSoloists: true,
    maxSoloists: 5,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  // Citation of Excellence Categories
  {
    id: generateId(),
    contestId: contestsData[1].id,
    name: "Concert Band",
    basePrice: 50,
    supportsSoloists: false,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: generateId(),
    contestId: contestsData[1].id,
    name: "Jazz Ensemble",
    basePrice: 50,
    supportsSoloists: false,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: generateId(),
    contestId: contestsData[1].id,
    name: "Orchestra",
    basePrice: 50,
    supportsSoloists: false,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: generateId(),
    contestId: contestsData[1].id,
    name: "Choir",
    basePrice: 50,
    supportsSoloists: false,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

// Classification data
const classificationsData = [
  // Standard classifications for most categories
  {
    id: generateId(),
    categoryId: categoriesData[0].id, // Concert Band MOE
    name: "A",
    price: 0, // Base price included in category
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: generateId(),
    categoryId: categoriesData[0].id,
    name: "AA",
    price: 0,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: generateId(),
    categoryId: categoriesData[0].id,
    name: "AAA",
    price: 0,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: generateId(),
    categoryId: categoriesData[0].id,
    name: "AAAA",
    price: 0,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: generateId(),
    categoryId: categoriesData[0].id,
    name: "AAAAA",
    price: 0,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  // Wind Band Honors (special multi-select classification)
  {
    id: generateId(),
    categoryId: categoriesData[2].id, // Wind Ensemble MOE
    name: "Wind Band Honors",
    price: 25, // Additional fee
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

// Competition state for current year
const competitionStateData = {
  id: generateId(),
  year: new Date().getFullYear(),
  registrationOpen: true,
  resultsLive: false,
  resultsMessage: "Results will be available after judging is complete.",
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Seed function
export async function seedDatabase() {
  try {
    console.log("Seeding database...");

    // Seed contests
    for (const contest of contestsData) {
      await contestsCollection.insert(contest);
    }
    console.log("✓ Contests seeded");

    // Seed categories
    for (const category of categoriesData) {
      await categoriesCollection.insert(category);
    }
    console.log("✓ Categories seeded");

    // Seed classifications
    for (const classification of classificationsData) {
      await classificationsCollection.insert(classification);
    }
    console.log("✓ Classifications seeded");

    // Seed competition state
    await competitionStateCollection.insert(competitionStateData);
    console.log("✓ Competition state seeded");

    // Seed demo accounts
    await seedDemoAccounts();

    console.log("Database seeding completed successfully!");
  } catch (error) {
    console.error("Error seeding database:", error);
    throw error;
  }
}

// Seed demo accounts for testing
async function seedDemoAccounts() {
  try {
    const { registerUser } = await import('./auth');
    
    // Check if demo accounts already exist
    const existingAdmin = await usersCollection.findFirst({
      where: (user) => user.email === '<EMAIL>'
    });
    
    const existingUser = await usersCollection.findFirst({
      where: (user) => user.email === '<EMAIL>'
    });

    // Create admin demo account
    if (!existingAdmin) {
      const adminResult = await registerUser({
        email: '<EMAIL>',
        password: 'Admin123!',
        firstName: 'Demo',
        lastName: 'Admin',
      });

      if (adminResult.success && adminResult.user) {
        // Update user to be admin
        await usersCollection.update(adminResult.user.id, {
          ...adminResult.user,
          isAdmin: true,
          updatedAt: new Date(),
        });
        console.log("✓ Demo admin account created");
      }
    }

    // Create regular demo account
    if (!existingUser) {
      const userResult = await registerUser({
        email: '<EMAIL>',
        password: 'User123!',
        firstName: 'Demo',
        lastName: 'User',
      });

      if (userResult.success) {
        console.log("✓ Demo user account created");
      }
    }
  } catch (error) {
    console.error("Error creating demo accounts:", error);
    // Don't throw error, just log it
  }
}

// Export seed data for reference
export {
  contestsData,
  categoriesData,
  classificationsData,
  competitionStateData,
};