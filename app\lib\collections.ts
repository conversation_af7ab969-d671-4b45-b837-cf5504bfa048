// Simple in-memory collections for testing authentication
// TODO: Replace with proper TanStack DB implementation once API is stable
import type {
  User,
  Contest,
  Category,
  Classification,
  Group,
  Director,
  ContactInfo,
  Selection,
  Soloist,
  Invoice,
  Judge,
  JudgeAssignment,
  JudgeEvaluation,
  VoiceMemo,
  DownloadCode,
  CompetitionState,
} from "./schema";

// Simple collection interface that mimics TanStack DB API
interface Collection<T> {
  insert: (item: T) => Promise<T>;
  update: (id: string, item: T) => Promise<T>;
  findFirst: (options?: { where?: (item: T) => boolean }) => Promise<T | null>;
  findMany: (options?: { where?: (item: T) => boolean }) => Promise<T[]>;
  delete: (id: string) => Promise<boolean>;
}

// In-memory storage
const storage = {
  users: new Map<string, User>(),
  contests: new Map<string, Contest>(),
  categories: new Map<string, Category>(),
  classifications: new Map<string, Classification>(),
  groups: new Map<string, Group>(),
  directors: new Map<string, Director>(),
  contactInfo: new Map<string, ContactInfo>(),
  selections: new Map<string, Selection>(),
  soloists: new Map<string, Soloist>(),
  invoices: new Map<string, Invoice>(),
  judges: new Map<string, Judge>(),
  judgeAssignments: new Map<string, JudgeAssignment>(),
  judgeEvaluations: new Map<string, JudgeEvaluation>(),
  voiceMemos: new Map<string, VoiceMemo>(),
  downloadCodes: new Map<string, DownloadCode>(),
  competitionState: new Map<string, CompetitionState>(),
};

// Create collection factory
function createCollection<T extends { id: string }>(
  storageMap: Map<string, T>
): Collection<T> {
  return {
    async insert(item: T): Promise<T> {
      storageMap.set(item.id, item);
      return item;
    },

    async update(id: string, item: T): Promise<T> {
      storageMap.set(id, item);
      return item;
    },

    async findFirst(options?: { where?: (item: T) => boolean }): Promise<T | null> {
      if (!options?.where) {
        const firstEntry = storageMap.values().next();
        return firstEntry.done ? null : firstEntry.value;
      }

      for (const item of storageMap.values()) {
        if (options.where(item)) {
          return item;
        }
      }
      return null;
    },

    async findMany(options?: { where?: (item: T) => boolean }): Promise<T[]> {
      const items = Array.from(storageMap.values());
      if (!options?.where) {
        return items;
      }
      return items.filter(options.where);
    },

    async delete(id: string): Promise<boolean> {
      return storageMap.delete(id);
    },
  };
}

// Export collections
export const usersCollection = createCollection(storage.users);
export const contestsCollection = createCollection(storage.contests);
export const categoriesCollection = createCollection(storage.categories);
export const classificationsCollection = createCollection(storage.classifications);
export const groupsCollection = createCollection(storage.groups);
export const directorsCollection = createCollection(storage.directors);
export const contactInfoCollection = createCollection(storage.contactInfo);
export const selectionsCollection = createCollection(storage.selections);
export const soloistsCollection = createCollection(storage.soloists);
export const invoicesCollection = createCollection(storage.invoices);
export const judgesCollection = createCollection(storage.judges);
export const judgeAssignmentsCollection = createCollection(storage.judgeAssignments);
export const judgeEvaluationsCollection = createCollection(storage.judgeEvaluations);
export const voiceMemosCollection = createCollection(storage.voiceMemos);
export const downloadCodesCollection = createCollection(storage.downloadCodes);
export const competitionStateCollection = createCollection(storage.competitionState);

// Collections are already exported above, no need to re-export