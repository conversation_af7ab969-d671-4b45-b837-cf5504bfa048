{"name": "music-judging-app", "version": "1.0.0", "description": "Music Competition Judging Platform", "main": "index.js", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "test:auth": "node test-auth.js"}, "keywords": ["music", "competition", "judging", "tanstack"], "author": "", "license": "ISC", "dependencies": {"@paralleldrive/cuid2": "^2.2.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/postcss": "^4.1.11", "@tanstack/react-db": "^0.1.0", "@tanstack/react-form": "^1.11.0", "@tanstack/react-query": "^5.71.10", "@tanstack/react-router": "^1.127.1", "@tanstack/react-start": "^1.130.8", "@tanstack/react-table": "^8.20.5", "@tanstack/router-vite-plugin": "^1.130.8", "@types/bcryptjs": "^3.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "jose": "^6.0.12", "lucide-react": "^0.525.0", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "vite": "^7.0.4", "zod": "^3.24.1", "zustand": "^5.0.2"}, "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "vite-tsconfig-paths": "^5.1.4"}}