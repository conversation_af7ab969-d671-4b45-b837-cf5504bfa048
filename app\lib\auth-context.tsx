// React context and hooks for authentication
import React, { createContext, useContext, useEffect, useState, ReactNode } from "react";
import {
  getAuthContext,
  setAuthToken,
  removeAuthToken,
  type AuthContext as AuthContextType
} from "./auth-middleware";
import { loginUser, registerUser, type LoginCredentials, type RegisterData, type AuthResult } from "./auth";

// Extended auth context with actions
interface AuthContextValue extends AuthContextType {
  login: (credentials: LoginCredentials) => Promise<AuthResult>;
  register: (data: RegisterData) => Promise<AuthResult>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  isLoading: boolean;
}

// Create context
const AuthContext = createContext<AuthContextValue | null>(null);

// Auth provider component
interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [authState, setAuthState] = useState<AuthContextType>({
    user: null,
    isAuthenticated: false,
    isAdmin: false,
    token: null,
  });
  const [isLoading, setIsLoading] = useState(true);

  // Initialize auth state
  useEffect(() => {
    console.log("Initializing auth context...");
    
    // For now, just set to unauthenticated state immediately
    // This will show the login form
    setAuthState({
      user: null,
      isAuthenticated: false,
      isAdmin: false,
      token: null,
    });
    setIsLoading(false);
    console.log("Auth initialized - showing login form");
  }, []);

  // Login function
  const login = async (credentials: LoginCredentials): Promise<AuthResult> => {
    try {
      const result = await loginUser(credentials);

      if (result.success && result.token && result.user) {
        setAuthToken(result.token);
        setAuthState({
          user: result.user,
          isAuthenticated: true,
          isAdmin: result.user.isAdmin,
          token: result.token,
        });
      }

      return result;
    } catch (error) {
      console.error("Login error:", error);
      return {
        success: false,
        error: "Login failed. Please try again.",
      };
    }
  };

  // Register function
  const register = async (data: RegisterData): Promise<AuthResult> => {
    try {
      const result = await registerUser(data);

      if (result.success && result.token && result.user) {
        setAuthToken(result.token);
        setAuthState({
          user: result.user,
          isAuthenticated: true,
          isAdmin: result.user.isAdmin,
          token: result.token,
        });
      }

      return result;
    } catch (error) {
      console.error("Registration error:", error);
      return {
        success: false,
        error: "Registration failed. Please try again.",
      };
    }
  };

  // Logout function
  const logout = async (): Promise<void> => {
    try {
      removeAuthToken();
      setAuthState({
        user: null,
        isAuthenticated: false,
        isAdmin: false,
        token: null,
      });

      // Redirect to home page
      if (typeof window !== 'undefined') {
        window.location.href = '/';
      }
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  // Refresh auth state
  const refreshAuth = async (): Promise<void> => {
    try {
      const context = await getAuthContext();
      setAuthState(context);
    } catch (error) {
      console.error("Auth refresh error:", error);
    }
  };

  const contextValue: AuthContextValue = {
    ...authState,
    login,
    register,
    logout,
    refreshAuth,
    isLoading,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook to use auth context
export function useAuth(): AuthContextValue {
  const context = useContext(AuthContext);

  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }

  return context;
}

// Hook for authenticated routes
export function useRequireAuth() {
  const auth = useAuth();

  useEffect(() => {
    if (!auth.isLoading && !auth.isAuthenticated) {
      if (typeof window !== 'undefined') {
        window.location.href = '/';
      }
    }
  }, [auth.isAuthenticated, auth.isLoading]);

  return auth;
}

// Hook for admin routes
export function useRequireAdmin() {
  const auth = useRequireAuth();

  useEffect(() => {
    if (!auth.isLoading && auth.isAuthenticated && !auth.isAdmin) {
      if (typeof window !== 'undefined') {
        window.location.href = '/dashboard';
      }
    }
  }, [auth.isAuthenticated, auth.isAdmin, auth.isLoading]);

  return auth;
}

// Hook for guest routes (redirect if authenticated)
export function useRequireGuest() {
  const auth = useAuth();

  useEffect(() => {
    if (!auth.isLoading && auth.isAuthenticated) {
      const redirectTo = auth.isAdmin ? "/admin" : "/dashboard";
      if (typeof window !== 'undefined') {
        window.location.href = redirectTo;
      }
    }
  }, [auth.isAuthenticated, auth.isAdmin, auth.isLoading]);

  return auth;
}