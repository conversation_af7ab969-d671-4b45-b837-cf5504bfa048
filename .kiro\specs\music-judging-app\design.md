# Design Document

## Overview

The Music Judging App is a comprehensive platform designed to facilitate music competitions by providing role-based dashboards for administrators, participants, and judges. Built with React 19, TanStack Start, and TypeScript, the system supports two main contest types: Mark of Excellence (MoE) at $400 per classification and Citation of Excellence (CoE) at $325 per entry, each with specific categories, classifications, and pricing structures. The platform enables multi-step registration, audio submissions, phased judging workflows, and comprehensive results management with payment processing and download code distribution. The architecture emphasizes security, scalability, and user experience across three distinct user journeys while maintaining historical data and providing flexible contest management capabilities.

## Contest Structure Design

### Contest Types and Pricing

The system supports two main contest types with distinct pricing models:

**Mark of Excellence (MoE)**: $400 per classification

- National Choral Honors
- National Jazz Honors
- National Orchestra Honors
- National Percussion Honors
- National Wind Band Honors

**Citation of Excellence (CoE)**: $325 per entry

- Citation of Excellence - Band
- Citation of Excellence - Orchestra

### Special Classification Rules

**Wind Band Honors Multi-Select**: Groups can register for both A-type classifications and New Music Division simultaneously, with separate fees for each classification selected.

**Soloist Support**: Categories can be configured to support soloist information collection, with timestamp and instrument details for outstanding soloist recognition.

### Judging Phases

The system supports flexible judging workflows:

- **No Phase**: Direct final judging for smaller competitions
- **Phase 1**: Initial screening with advancement selection (typically 50% advance)
- **Phase 2**: Final judging with award selection (25% National/Citation, 25% Commended)

### Award Categories

**Mark of Excellence Awards**:

- National Award (top 25%)
- Commended Award (next 25%)
- Outstanding Soloists

**Citation of Excellence Awards**:

- Citation Award (top 25%)
- Commended Award (next 25%)
- Outstanding Soloists

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        A[React Frontend]
        B[TanStack Router]
        C[shadcn/ui Components]
    end

    subgraph "Server Layer"
        D[Fastify Server]
        E[API Routes]
        F[Authentication Middleware]
        G[File Upload Handler]
    end

    subgraph "Data Layer"
        H[TanStack DB]
        I[File Storage - MinIO]
    end

    subgraph "External Services"
        K[Stripe Payment API]
        L[Email Service - SendGrid]
        M[Audio Processing Service]
    end

    A --> D
    B --> E
    D --> H
    G --> I
    E --> K
    E --> L
```

### Technology Stack

**Frontend:**

- React 19 with TypeScript
- TanStack Start framework
- TanStack Forms for forms
- TanStack Query for server state
- TanStack Table for data tables
- Zustand for client-side state management
- shadcn/ui components with Tailwind CSS for styling
- Lucide React for icons
- Custom audio player components

**Backend:**

- Fastify server with TypeScript
- RESTful API endpoints
- JWT-based authentication middleware
- Multipart file upload handling with validation

**Database:**

- TanStack DB for type-safe, reactive data layer
- Built-in real-time sync and offline capabilities
- Automatic schema validation and migrations
- Integrated with TanStack ecosystem for seamless data flow
- CUID2 for all unique identifiers (collision-resistant, URL-safe, sortable)

**File Storage:**

- MinIO for S3-compatible object storage
- Audio file storage with signed URLs
- File upload validation and processing

**External Services:**

- Stripe for payment processing
- SendGrid for email notifications
- Audio processing service for file validation

## Components and Interfaces

### Core Data Models

#### User Model

```typescript
interface User {
  id: string;
  email: string;
  passwordHash: string;
  firstName: string;
  lastName: string;
  isAdmin: boolean;
  groupIds: string[];
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;
}
```

#### Contest Models

```typescript
interface CompetitionState {
  id: string;
  registrationOpen: boolean;
  registrationMessage?: string;
  resultsLive: boolean;
  resultsMessage?: string;
}

interface Contest {
  id: string;
  name: "Mark of Excellence" | "Citation of Excellence";
  isActive: boolean;
  createdAt: Date;
}

interface Category {
  id: string;
  contestId: string;
  name: string;
  hasSoloists: boolean;
  requiredSelections: number;
  isOptional?: boolean;
  pricing: number;
}

interface Classification {
  id: string;
  categoryId: string;
  name: string;
  allowMultiSelect?: boolean;
}
```

#### Group Models

```typescript
interface Group {
  id: string;
  userId: string;
  contestId: string;
  categoryId: string;
  classificationIds: string[];

  // Director Information
  directors: Director[];

  // School Information
  ensembleName: string;
  schoolName: string;
  schoolEnrollment: number;

  // Contact Information
  cellPhone: string;
  mailingAddress: Address;
  shippingAddress?: Address;

  // Masked Names for Anonymization
  maskedNames: Record<string, string>; // classificationId -> masked name (A, B, C, ..., AA, BB, etc.)

  // Selections
  tracks: Selection[];

  // Payment
  invoiceId?: string;
  paymentStatus: "pending" | "paid" | "failed";

  // Status
  isArchived: boolean;
  submittedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface Selection {
  id: string;
  groupId: string;
  selectionNumber: number;
  title: string;
  composerArranger: string;
  duration: string;
  recordingDate: Date;
  recordingVenue: string;
  audioFileUrl?: string;
  audioFileName?: string;
  soloists: Soloist[];
}

interface Soloist {
  id: string;
  selectionId: string;
  name: string;
  instrument: string;
  timestamp?: string;
}
```

#### Invoice Models

```typescript
interface Invoice {
  id: string;
  groupId: string;
  calculatedAmount: number; // Auto-calculated from group classifications
  manualAdjustment: number; // Optional manual adjustment (discounts, fees)
  finalAmount: number; // calculatedAmount + manualAdjustment
  description: string;
  lineItems: InvoiceLineItem[]; // Breakdown of classification pricing
  status: "draft" | "sent" | "paid" | "failed" | "cancelled";
  stripePaymentIntentId?: string;
  stripeInvoiceId?: string;
  paymentMethod?: string;
  paidAt?: Date;
  dueDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface InvoiceLineItem {
  classificationId: string;
  classificationName: string;
  contestType: "MARK_OF_EXCELLENCE" | "CITATION_OF_EXCELLENCE";
  price: number;
  description: string;
}
```

#### Judge Models

```typescript
interface Judge {
  id: string;
  uniqueToken: string;
  firstName: string;
  lastName: string;
  email: string;
  introVideoUrl?: string;
  isActive: boolean;
  assignments: JudgeAssignment[];
  createdAt: Date;
}

interface JudgeAssignment {
  id: string;
  categoryId: string;
  phase?: "none" | "phase1" | "phase2";
  classifications: ClassificationAssignment[];
  createdAt: Date;
}

interface ClassificationAssignment {
  classificationId: string;
  groupIds: string[]; // Specific groups within this classification
}

// Example Judge Assignment Structure:
// {
//   id: "clx1234567890abcdef", // cuid
//   categoryId: "clx0987654321fedcba", // cuid referencing categories table
//   phase: "phase1",
//   classifications: [
//     {
//       classificationId: "clx1111222233334444", // cuid referencing classifications table
//       groupIds: ["clx5555666677778888", "clx9999aaaabbbbcccc", "clxddddeeeeffffgggg"] // cuids referencing groups table
//     },
//     {
//       classificationId: "clx2222333344445555",
//       groupIds: ["clx6666777788889999", "clxaaaabbbbccccdddd"]
//     }
//   ],
//   createdAt: new Date()
// }

interface JudgeEvaluation {
  id: string;
  judgeId: string;
  assignmentId: string; // Reference to assignment within judge.assignments array
  groupId: string;
  selectionId: string;
  score?: number;
  writtenComments?: string;
  voiceMemos: VoiceMemo[];
  outstandingSoloistIds: string[];

  // Phase-specific selections
  movesOn?: boolean; // Phase 1
  nationalAward?: boolean; // MoE Phase 2/No Phase
  citationAward?: boolean; // CoE Phase 2/No Phase
  commendedAward?: boolean; // Phase 2/No Phase

  isComplete: boolean;
  submittedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface VoiceMemo {
  id: string;
  evaluationId: string;
  audioUrl: string;
  duration: number;
  timestamp: number;
  createdAt: Date;
}
```

### Component Architecture

#### Authentication Components

- `LoginForm` - User login with email/password
- `RegisterForm` - User registration
- `ForgotPasswordForm` - Password reset request
- `ResetPasswordForm` - Password reset completion
- `AuthGuard` - Route protection wrapper

#### Admin Dashboard Components

- `AdminDashboard` - Main admin overview
- `ContestManager` - Contest configuration and settings
- `CategoryManager` - Category and classification management
- `JudgeManager` - Judge creation, assignment, and communication
- `GroupManager` - Group viewing, editing, and management
- `UserManager` - User management and impersonation
- `ResultsManager` - Results export and download code management

#### User Dashboard Components

- `UserDashboard` - Main user overview with current and previous year entries
- `GroupList` - Display user's groups by year with status information
- `GroupCard` - Individual group summary with payment status and actions
- `RegistrationWizard` - 6-step registration form with validation at each step:
  1. Director Information (prefix, first name, last name, multiple directors)
  2. School Information (ensemble name, school name, enrollment)
  3. Contest Details (contest, category, classification selection with multi-select for Wind Band Honors)
  4. Contact Information (phone, mailing address, optional shipping address)
  5. Music Selections (title, composer/arranger, duration, recording date, venue, audio upload, soloists)
  6. Review and Submit
- `PaymentInterface` - Stripe payment integration with payment link generation
- `ResultsViewer` - Display results with judge voice memos, notes, and download codes
- `AudioUploadManager` - Audio file upload with .wav/.mp3 validation and missing file alerts

#### Judge Dashboard Components

- `JudgeDashboard` - Main judge overview
- `IntroVideoRecorder` - Video introduction creation
- `AssignmentList` - Judge's assigned categories
- `ClassificationDashboard` - Classification-specific judging interface
- `GroupEvaluator` - Individual group evaluation interface
- `AudioPlayer` - Custom audio playback with memo recording
- `ScoringInterface` - Scoring and selection interface

#### Shared Components

- `AudioUploader` - File upload with validation
- `AudioPlayer` - Playback controls with seeking
- `VoiceMemoRecorder` - Audio recording interface
- `ProgressIndicator` - Multi-step form progress
- `DataTable` - Sortable, filterable tables
- `NotificationSystem` - Toast notifications
- `LoadingSpinner` - Loading states
- `ErrorBoundary` - Error handling

## Data Models

### TanStack DB Schema

#### Database Configuration

```typescript
import { createDB } from "@tanstack/db";
import { createId } from "@paralleldrive/cuid2";

// Define the database schema with TanStack DB
export const db = createDB({
  name: "music-judging-app",
  version: 1,
  tables: {
    users: {
      id: { type: "string", primaryKey: true, default: () => createId() },
      email: { type: "string", unique: true },
      passwordHash: { type: "string" },
      firstName: { type: "string" },
      lastName: { type: "string" },
      role: {
        type: "enum",
        values: ["ADMIN", "PARTICIPANT"],
        default: "PARTICIPANT",
      },
      isActive: { type: "boolean", default: true },
      lastLoginAt: { type: "date", nullable: true },
      createdAt: { type: "date", default: () => new Date() },
      updatedAt: { type: "date", default: () => new Date() },
    },

    competitions: {
      id: { type: "string", primaryKey: true, default: () => createId() },
      currentYear: { type: "number" },
      registrationOpen: { type: "boolean", default: false },
      registrationMessage: { type: "string", nullable: true },
      resultsLive: { type: "boolean", default: false },
      resultsMessage: { type: "string", nullable: true },
      createdAt: { type: "date", default: () => new Date() },
      updatedAt: { type: "date", default: () => new Date() },
    },

    contests: {
      id: { type: "string", primaryKey: true, default: () => createId() },
      name: {
        type: "enum",
        values: ["MARK_OF_EXCELLENCE", "CITATION_OF_EXCELLENCE"],
      },
      isActive: { type: "boolean", default: true },
      createdAt: { type: "date", default: () => new Date() },
    },

    categories: {
      id: { type: "string", primaryKey: true, default: () => createId() },
      contestId: { type: "string", references: "contests.id" },
      name: { type: "string" },
      hasSoloists: { type: "boolean", default: false },
      requiredSelections: { type: "number", default: 2 },
      isOptional: { type: "boolean", default: false },
      pricing: { type: "number" },
    },

    classifications: {
      id: { type: "string", primaryKey: true, default: () => createId() },
      categoryId: { type: "string", references: "categories.id" },
      name: { type: "string" },
      allowMultiSelect: { type: "boolean", default: false },
    },

    groups: {
      id: { type: "string", primaryKey: true, default: () => createId() },
      userId: { type: "string", references: "users.id" },
      contestId: { type: "string", references: "contests.id" },
      categoryId: { type: "string", references: "categories.id" },
      ensembleName: { type: "string" },
      schoolName: { type: "string" },
      schoolEnrollment: { type: "number", nullable: true },
      cellPhone: { type: "string", nullable: true },
      mailingAddress: { type: "json", nullable: true },
      shippingAddress: { type: "json", nullable: true },
      maskedNames: { type: "json", default: {} }, // Object mapping classificationId to masked name (e.g., {"clx123": "A", "clx456": "AA"})
      invoiceId: { type: "string", nullable: true, references: "invoices.id" },
      paymentStatus: {
        type: "enum",
        values: ["PENDING", "PAID", "FAILED"],
        default: "PENDING",
      },
      status: {
        type: "enum",
        values: ["DRAFT", "SUBMITTED", "ARCHIVED"],
        default: "DRAFT",
      },
      submittedAt: { type: "date", nullable: true },
      createdAt: { type: "date", default: () => new Date() },
      updatedAt: { type: "date", default: () => new Date() },
    },

    groupClassifications: {
      groupId: { type: "string", references: "groups.id" },
      classificationId: { type: "string", references: "classifications.id" },
      primaryKey: ["groupId", "classificationId"],
    },

    directors: {
      id: { type: "string", primaryKey: true, default: () => createId() },
      groupId: { type: "string", references: "groups.id" },
      prefix: { type: "string", nullable: true },
      firstName: { type: "string" },
      lastName: { type: "string" },
    },

    selections: {
      id: { type: "string", primaryKey: true, default: () => createId() },
      groupId: { type: "string", references: "groups.id" },
      selectionNumber: { type: "number" },
      title: { type: "string" },
      composerArranger: { type: "string" },
      duration: { type: "string" },
      recordingDate: { type: "date" },
      recordingVenue: { type: "string" },
      audioFileUrl: { type: "string", nullable: true },
      audioFileName: { type: "string", nullable: true },
      createdAt: { type: "date", default: () => new Date() },
    },

    soloists: {
      id: { type: "string", primaryKey: true, default: () => createId() },
      selectionId: { type: "string", references: "selections.id" },
      name: { type: "string" },
      instrument: { type: "string" },
      timestamp: { type: "string", nullable: true },
    },

    invoices: {
      id: { type: "string", primaryKey: true, default: () => createId() },
      groupId: { type: "string", references: "groups.id" },
      calculatedAmount: { type: "number", computed: true }, // Auto-calculated from group classifications
      manualAdjustment: { type: "number", default: 0 }, // Optional manual adjustment (discounts, fees)
      finalAmount: { type: "number", computed: true }, // calculatedAmount + manualAdjustment
      description: { type: "string" },
      lineItems: { type: "json", computed: true }, // Array of classification pricing breakdown
      status: {
        type: "enum",
        values: ["DRAFT", "SENT", "PAID", "FAILED", "CANCELLED"],
        default: "DRAFT",
      },
      stripePaymentIntentId: { type: "string", nullable: true },
      stripeInvoiceId: { type: "string", nullable: true },
      paymentMethod: { type: "string", nullable: true },
      paidAt: { type: "date", nullable: true },
      dueDate: { type: "date", nullable: true },
      createdAt: { type: "date", default: () => new Date() },
      updatedAt: { type: "date", default: () => new Date() },
    },

    judges: {
      id: { type: "string", primaryKey: true, default: () => createId() },
      uniqueToken: { type: "string", unique: true, default: () => createId() },
      firstName: { type: "string" },
      lastName: { type: "string" },
      email: { type: "string" },
      introVideoUrl: { type: "string", nullable: true },
      isActive: { type: "boolean", default: true },
      assignments: { type: "json", default: [] }, // Array of JudgeAssignment objects with nested ClassificationAssignment
      createdAt: { type: "date", default: () => new Date() },
    },

    judgeEvaluations: {
      id: { type: "string", primaryKey: true, default: () => createId() },
      judgeId: { type: "string", references: "judges.id" },
      assignmentId: { type: "string" }, // Reference to assignment within judge.assignments array
      groupId: { type: "string", references: "groups.id" },
      selectionId: { type: "string", references: "selections.id" },
      score: { type: "number", nullable: true },
      writtenComments: { type: "string", nullable: true },
      outstandingSoloistIds: { type: "json", default: [] },
      movesOn: { type: "boolean", nullable: true },
      nationalAward: { type: "boolean", nullable: true },
      citationAward: { type: "boolean", nullable: true },
      commendedAward: { type: "boolean", nullable: true },
      isComplete: { type: "boolean", default: false },
      submittedAt: { type: "date", nullable: true },
      createdAt: { type: "date", default: () => new Date() },
      updatedAt: { type: "date", default: () => new Date() },
    },

    voiceMemos: {
      id: { type: "string", primaryKey: true, default: () => createId() },
      evaluationId: { type: "string", references: "judgeEvaluations.id" },
      audioUrl: { type: "string" },
      duration: { type: "number" },
      timestampPosition: { type: "number" },
      createdAt: { type: "date", default: () => new Date() },
    },

    downloadCodes: {
      id: { type: "string", primaryKey: true, default: () => createId() },
      groupId: { type: "string", references: "groups.id" },
      classificationId: { type: "string", references: "classifications.id" },
      year: { type: "number" },
      code: { type: "string" },
      createdAt: { type: "date", default: () => new Date() },
      unique: [["groupId", "year"]],
    },
  },
});
```

#### Masked Name Generation

The system generates alphabetical identifiers for groups within each classification to maintain anonymity during judging:

```typescript
// Utility function to generate masked names
export function generateMaskedName(position: number): string {
  const alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  let result = "";
  let num = position;

  do {
    result = alphabet[num % 26] + result;
    num = Math.floor(num / 26);
  } while (num > 0);

  return result;
}

// Examples:
// generateMaskedName(0) -> "A"
// generateMaskedName(25) -> "Z"
// generateMaskedName(26) -> "AA"
// generateMaskedName(51) -> "AZ"
// generateMaskedName(52) -> "BA"

// Function to assign masked names when group is submitted
export async function assignMaskedNames(
  groupId: string,
  classificationIds: string[]
) {
  const maskedNames: Record<string, string> = {};

  for (const classificationId of classificationIds) {
    // Count existing submitted groups in this classification
    const existingGroups = await db.groups.findMany({
      where: {
        status: "SUBMITTED",
        // Check if this classification exists in their groupClassifications
      },
    });

    // Filter groups that have this classification
    const groupsInClassification = existingGroups.filter(
      (group) => group.maskedNames && group.maskedNames[classificationId]
    );

    // Generate next masked name
    const position = groupsInClassification.length;
    maskedNames[classificationId] = generateMaskedName(position);
  }

  // Update group with masked names
  await db.groups.update({
    where: { id: groupId },
    data: { maskedNames },
  });

  return maskedNames;
}

// Example result for a group entering 2A and New Music classifications:
// {
//   "clx123-2a-classification": "AA",  // 27th group in 2A
//   "clx456-new-music-classification": "F"  // 6th group in New Music
// }
```

#### Invoice Auto-Calculation

Invoices automatically calculate totals based on group classifications and contest pricing:

```typescript
// Function to calculate invoice amounts based on group classifications
export async function calculateInvoiceAmount(groupId: string): Promise<{
  calculatedAmount: number;
  lineItems: InvoiceLineItem[];
}> {
  // Get group with classifications
  const group = await db.groups.findFirst({
    where: { id: groupId },
    include: {
      groupClassifications: {
        include: {
          classification: {
            include: {
              category: {
                include: {
                  contest: true,
                },
              },
            },
          },
        },
      },
    },
  });

  if (!group) throw new Error("Group not found");

  const lineItems: InvoiceLineItem[] = [];
  let calculatedAmount = 0;

  for (const groupClassification of group.groupClassifications) {
    const classification = groupClassification.classification;
    const category = classification.category;
    const contest = category.contest;

    // Determine pricing based on contest type
    let price: number;
    if (contest.name === "MARK_OF_EXCELLENCE") {
      price = 400; // $400 per classification for MoE
    } else if (contest.name === "CITATION_OF_EXCELLENCE") {
      price = 325; // $325 per entry for CoE
    } else {
      price = category.pricing; // Fallback to category pricing
    }

    const lineItem: InvoiceLineItem = {
      classificationId: classification.id,
      classificationName: classification.name,
      contestType: contest.name,
      price,
      description: `${
        contest.name === "MARK_OF_EXCELLENCE"
          ? "Mark of Excellence"
          : "Citation of Excellence"
      } - ${classification.name}`,
    };

    lineItems.push(lineItem);
    calculatedAmount += price;
  }

  return { calculatedAmount, lineItems };
}

// Function to update invoice when group classifications change
export async function updateInvoiceForGroup(groupId: string) {
  const { calculatedAmount, lineItems } = await calculateInvoiceAmount(groupId);

  // Find existing invoice for this group
  const existingInvoice = await db.invoices.findFirst({
    where: { groupId },
  });

  if (existingInvoice) {
    // Update existing invoice
    const finalAmount =
      calculatedAmount + (existingInvoice.manualAdjustment || 0);

    await db.invoices.update({
      where: { id: existingInvoice.id },
      data: {
        calculatedAmount,
        finalAmount,
        lineItems,
        updatedAt: new Date(),
      },
    });
  } else {
    // Create new invoice
    await db.invoices.create({
      data: {
        id: createId(),
        groupId,
        calculatedAmount,
        manualAdjustment: 0,
        finalAmount: calculatedAmount,
        lineItems,
        description: `Registration fees for ${lineItems.length} classification${
          lineItems.length > 1 ? "s" : ""
        }`,
        status: "DRAFT",
      },
    });
  }
}

// Function to apply manual adjustments (discounts, additional fees)
export async function applyInvoiceAdjustment(
  invoiceId: string,
  adjustment: number,
  reason: string
) {
  const invoice = await db.invoices.findFirst({ where: { id: invoiceId } });
  if (!invoice) throw new Error("Invoice not found");

  const finalAmount = invoice.calculatedAmount + adjustment;

  await db.invoices.update({
    where: { id: invoiceId },
    data: {
      manualAdjustment: adjustment,
      finalAmount,
      description: invoice.description + (reason ? ` (${reason})` : ""),
      updatedAt: new Date(),
    },
  });
}

// Example invoice calculation:
// Group enters: National Wind Band Honors (A-type) + National Wind Band Honors (New Music)
// Line Items:
// - Mark of Excellence - National Wind Band Honors A-type: $400
// - Mark of Excellence - National Wind Band Honors New Music: $400
// Calculated Amount: $800
// Manual Adjustment: -$50 (early bird discount)
// Final Amount: $750
```

### TanStack DB Integration

TanStack DB provides built-in real-time sync, offline capabilities, and seamless integration with the TanStack ecosystem:

#### TanStack DB Configuration

```typescript
import { createDB, createQuery } from "@tanstack/db";
import { useQuery } from "@tanstack/react-query";

// Initialize database with real-time sync
const db = createDB({
  name: "music-judging-app",
  sync: {
    url: process.env.TANSTACK_DB_SYNC_URL,
    token: process.env.TANSTACK_DB_TOKEN,
    realtime: true,
  },
});

// Real-time queries with automatic updates
export const useCompetitionState = () => {
  return useQuery({
    queryKey: ["competition"],
    queryFn: () => db.competitions.findFirst(),
    // Automatically updates when data changes
    refetchOnWindowFocus: false,
    staleTime: Infinity, // Data is always fresh with real-time sync
  });
};

// Judge evaluation progress with live updates
export const useJudgeProgress = (judgeId: string) => {
  return useQuery({
    queryKey: ["judge-evaluations", judgeId],
    queryFn: () =>
      db.judgeEvaluations.findMany({
        where: { judgeId },
        include: { group: true, selection: true },
      }),
  });
};
```

#### Real-time Features

- **Competition State**: Automatic UI updates when registration opens/closes or results go live
- **Judge Progress**: Live evaluation progress tracking with automatic dashboard updates
- **Group Status**: Real-time payment and submission status changes
- **Results**: Instant results publication across all connected clients
- **Judge Assignments**: Live updates to judge assignments and workload distribution
- **Form Sync**: Real-time form data synchronization across browser tabs

#### Offline Capabilities

- **Offline-First Architecture**: All data operations work offline with automatic sync when reconnected
- **Judge Evaluations**: Complete judging workflows available offline with background sync
- **Group Drafts**: Full registration wizard functionality offline
- **Audio Playback**: Cached audio files with offline playback support
- **Conflict Resolution**: Automatic conflict resolution for concurrent edits
- **Queue Management**: Offline operations queued and executed when connection restored

#### TanStack Ecosystem Integration

- **TanStack Query**: Seamless integration for server state management
- **TanStack Forms**: Direct database binding for form validation and submission
- **TanStack Table**: Real-time data tables with automatic updates
- **TanStack Router**: Type-safe routing with database-driven navigation

### Zustand State Management

Zustand handles client-side UI state and user preferences that don't need to be persisted to the database:

#### Store Structures

```typescript
import { create } from "zustand";
import { persist } from "zustand/middleware";

// UI State Store
interface UIState {
  // Modal states
  isLoginModalOpen: boolean;
  isPaymentModalOpen: boolean;
  isAudioPlayerModalOpen: boolean;

  // Navigation
  sidebarCollapsed: boolean;
  currentWizardStep: number;

  // Loading states
  isUploading: boolean;
  uploadProgress: number;

  // Actions
  openModal: (modal: "login" | "payment" | "audioPlayer") => void;
  closeModal: (modal: "login" | "payment" | "audioPlayer") => void;
  toggleSidebar: () => void;
  setWizardStep: (step: number) => void;
  setUploadProgress: (progress: number) => void;
}

export const useUIStore = create<UIState>((set) => ({
  isLoginModalOpen: false,
  isPaymentModalOpen: false,
  isAudioPlayerModalOpen: false,
  sidebarCollapsed: false,
  currentWizardStep: 1,
  isUploading: false,
  uploadProgress: 0,

  openModal: (modal) =>
    set((state) => ({
      ...state,
      [`is${modal.charAt(0).toUpperCase() + modal.slice(1)}ModalOpen`]: true,
    })),
  closeModal: (modal) =>
    set((state) => ({
      ...state,
      [`is${modal.charAt(0).toUpperCase() + modal.slice(1)}ModalOpen`]: false,
    })),
  toggleSidebar: () =>
    set((state) => ({ sidebarCollapsed: !state.sidebarCollapsed })),
  setWizardStep: (step) => set({ currentWizardStep: step }),
  setUploadProgress: (progress) =>
    set({ uploadProgress: progress, isUploading: progress < 100 }),
}));

// User Preferences Store (Persisted)
interface UserPreferences {
  theme: "light" | "dark" | "system";
  language: "en" | "es" | "fr";
  dashboardLayout: "grid" | "list";
  audioPlayerVolume: number;
  autoPlayNext: boolean;

  // Actions
  setTheme: (theme: "light" | "dark" | "system") => void;
  setLanguage: (language: "en" | "es" | "fr") => void;
  setDashboardLayout: (layout: "grid" | "list") => void;
  setAudioPlayerVolume: (volume: number) => void;
  toggleAutoPlayNext: () => void;
}

export const usePreferencesStore = create<UserPreferences>()(
  persist(
    (set) => ({
      theme: "system",
      language: "en",
      dashboardLayout: "grid",
      audioPlayerVolume: 0.8,
      autoPlayNext: true,

      setTheme: (theme) => set({ theme }),
      setLanguage: (language) => set({ language }),
      setDashboardLayout: (layout) => set({ dashboardLayout: layout }),
      setAudioPlayerVolume: (volume) => set({ audioPlayerVolume: volume }),
      toggleAutoPlayNext: () =>
        set((state) => ({ autoPlayNext: !state.autoPlayNext })),
    }),
    {
      name: "user-preferences",
    }
  )
);

// Audio Player Store
interface AudioPlayerState {
  currentTrack: {
    id: string;
    title: string;
    url: string;
    duration: number;
  } | null;
  isPlaying: boolean;
  currentTime: number;
  volume: number;
  playbackRate: number;
  playlist: Array<{
    id: string;
    title: string;
    url: string;
    duration: number;
  }>;
  currentIndex: number;

  // Actions
  loadTrack: (track: AudioPlayerState["currentTrack"]) => void;
  play: () => void;
  pause: () => void;
  seek: (time: number) => void;
  setVolume: (volume: number) => void;
  setPlaybackRate: (rate: number) => void;
  loadPlaylist: (playlist: AudioPlayerState["playlist"]) => void;
  nextTrack: () => void;
  previousTrack: () => void;
}

export const useAudioPlayerStore = create<AudioPlayerState>((set, get) => ({
  currentTrack: null,
  isPlaying: false,
  currentTime: 0,
  volume: 0.8,
  playbackRate: 1.0,
  playlist: [],
  currentIndex: -1,

  loadTrack: (track) => set({ currentTrack: track, currentTime: 0 }),
  play: () => set({ isPlaying: true }),
  pause: () => set({ isPlaying: false }),
  seek: (time) => set({ currentTime: time }),
  setVolume: (volume) => set({ volume }),
  setPlaybackRate: (rate) => set({ playbackRate: rate }),
  loadPlaylist: (playlist) => set({ playlist, currentIndex: 0 }),
  nextTrack: () => {
    const { playlist, currentIndex } = get();
    const nextIndex = currentIndex + 1;
    if (nextIndex < playlist.length) {
      set({
        currentIndex: nextIndex,
        currentTrack: playlist[nextIndex],
        currentTime: 0,
      });
    }
  },
  previousTrack: () => {
    const { playlist, currentIndex } = get();
    const prevIndex = currentIndex - 1;
    if (prevIndex >= 0) {
      set({
        currentIndex: prevIndex,
        currentTrack: playlist[prevIndex],
        currentTime: 0,
      });
    }
  },
}));

// Form Draft Store (for multi-step forms)
interface FormDraftState {
  registrationDraft: Partial<{
    directors: Array<{ prefix?: string; firstName: string; lastName: string }>;
    schoolInfo: {
      ensembleName: string;
      schoolName: string;
      enrollment: number;
    };
    contestDetails: {
      contestId: string;
      categoryId: string;
      classificationIds: string[];
    };
    contactInfo: { phone: string; mailingAddress: any; shippingAddress?: any };
    selections: Array<{
      title: string;
      composerArranger: string;
      duration: string;
      recordingDate: Date;
      venue: string;
      soloists: Array<{ name: string; instrument: string; timestamp?: string }>;
    }>;
  }>;

  // Actions
  updateRegistrationDraft: (
    data: Partial<FormDraftState["registrationDraft"]>
  ) => void;
  clearRegistrationDraft: () => void;
}

export const useFormDraftStore = create<FormDraftState>()(
  persist(
    (set) => ({
      registrationDraft: {},

      updateRegistrationDraft: (data) =>
        set((state) => ({
          registrationDraft: { ...state.registrationDraft, ...data },
        })),
      clearRegistrationDraft: () => set({ registrationDraft: {} }),
    }),
    {
      name: "form-drafts",
    }
  )
);
```

### API Endpoints

#### Authentication Endpoints

```typescript
// POST /api/auth/login
interface LoginRequest {
  email: string;
  password: string;
}

// POST /api/auth/register
interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

// POST /api/auth/forgot-password
interface ForgotPasswordRequest {
  email: string;
}

// POST /api/auth/reset-password
interface ResetPasswordRequest {
  token: string;
  password: string;
}
```

#### User Endpoints

```typescript
// GET /api/user/groups
interface GetGroupsResponse {
  groups: Group[];
  currentYear: number;
}

// POST /api/user/groups
interface CreateGroupRequest {
  contestId: string;
  categoryId: string;
  classificationIds: string[];
  // ... other group fields
}

// PUT /api/user/groups/:id
interface UpdateGroupRequest {
  // Partial group update
}

// POST /api/user/groups/:id/payment
interface CreatePaymentRequest {
  amount: number;
  generateLink?: boolean;
}
```

#### Admin Endpoints

```typescript
// GET /api/admin/contests
// POST /api/admin/contests
// PUT /api/admin/contests/:id

// GET /api/admin/judges
// POST /api/admin/judges
// PUT /api/admin/judges/:id
// DELETE /api/admin/judges/:id

// GET /api/admin/groups
// PUT /api/admin/groups/:id
// DELETE /api/admin/groups/:id

// POST /api/admin/download-codes
interface UploadDownloadCodesRequest {
  csvData: string;
  year: number;
}

// GET /api/admin/results/export
interface ExportResultsRequest {
  year: number;
  format: "results" | "soloists";
}
```

#### Judge Endpoints

```typescript
// GET /api/judge/:token/dashboard
interface JudgeDashboardResponse {
  judge: Judge; // Contains embedded assignments array
  progress: AssignmentProgress[];
}

// POST /api/judge/:token/intro-video
interface UploadIntroVideoRequest {
  videoFile: File;
}

// GET /api/judge/:token/assignments/:assignmentId
interface AssignmentDetailsResponse {
  assignment: JudgeAssignment; // From judge.assignments array
  groups: Group[];
  evaluations: JudgeEvaluation[];
}

// POST /api/judge/:token/evaluations
// PUT /api/judge/:token/evaluations/:id
interface EvaluationRequest {
  assignmentId: string;
  groupId: string;
  selectionId: string;
  score?: number;
  writtenComments?: string;
  outstandingSoloistIds: string[];
  // Phase-specific selections
}

// POST /api/judge/:token/voice-memos
interface VoiceMemoRequest {
  evaluationId: string;
  audioFile: File;
  timestampPosition: number;
}
```

## Audio File Management and Historical Data

### Audio File Lifecycle

**File Storage Strategy**:

- Audio files are stored in MinIO with year-based organization
- Files are maintained for the current competition year
- Files older than 2 years are automatically deleted to manage storage costs
- Users receive notifications about upcoming file deletions

**File Format Support**:

- Accepted formats: .wav and .mp3
- File validation occurs before upload with clear error messaging
- Corrupted or invalid files are rejected with specific feedback

**Missing File Handling**:

- Registration can be completed without audio files
- System alerts users about missing files with clear indicators
- Missing file status is displayed prominently in user dashboards
- Reminder notifications are sent for incomplete submissions

### Historical Data Management

**Year-Based Data Organization**:

- All competition data is organized by year for historical tracking
- Users can toggle between current and previous years in their dashboard
- Results and download codes are maintained indefinitely
- Audio files are the only data type subject to automatic deletion

**Data Retention Policy**:

- Competition results: Permanent retention
- User accounts and group information: Permanent retention
- Judge evaluations and voice memos: Permanent retention
- Audio files: 2-year retention with deletion notice
- Download codes: Permanent retention for historical access

**Archive Functionality**:

- Groups can be archived by administrators
- Archived groups remain in historical data but are hidden from active views
- Archive status can be toggled for data management purposes

## Error Handling

### Error Types and Responses

```typescript
interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}

// Common error codes
enum ErrorCodes {
  VALIDATION_ERROR = "VALIDATION_ERROR",
  AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR",
  AUTHORIZATION_ERROR = "AUTHORIZATION_ERROR",
  NOT_FOUND = "NOT_FOUND",
  PAYMENT_ERROR = "PAYMENT_ERROR",
  FILE_UPLOAD_ERROR = "FILE_UPLOAD_ERROR",
  AUDIO_PROCESSING_ERROR = "AUDIO_PROCESSING_ERROR",
  DATABASE_ERROR = "DATABASE_ERROR",
  EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR",
}
```

### Error Handling Strategy

1. **Client-Side Error Handling**

   - React Error Boundaries for component-level errors
   - Form validation with real-time feedback
   - Network error handling with retry mechanisms
   - User-friendly error messages and recovery options

2. **Server-Side Error Handling**

   - Centralized error handling middleware
   - Structured error logging with context
   - Graceful degradation for external service failures
   - Database transaction rollbacks on errors

3. **File Upload Error Handling**

   - File format validation before upload
   - File size limit enforcement
   - Audio file corruption detection
   - Storage service error recovery

4. **Payment Error Handling**
   - Stripe webhook error handling
   - Payment retry mechanisms
   - Failed payment notifications
   - Invoice status synchronization

## Testing Strategy

### Unit Testing

- **Components**: React Testing Library for UI components
- **Business Logic**: Jest for utility functions and data processing
- **API Endpoints**: Supertest for server function testing
- **Database**: In-memory database for data layer testing

### Integration Testing

- **Authentication Flow**: End-to-end login/registration testing
- **Registration Wizard**: Multi-step form completion testing
- **Payment Processing**: Stripe integration testing with test mode
- **File Upload**: Audio file upload and validation testing
- **Judge Workflow**: Complete judging process testing

### End-to-End Testing

- **User Journeys**: Playwright for complete user workflows
- **Cross-Browser Testing**: Chrome, Firefox, Safari compatibility
- **Mobile Responsiveness**: Touch device interaction testing
- **Performance Testing**: Load testing for concurrent users

### Testing Data Management

- **Seed Data**: Consistent test data for development and testing
- **Mock Services**: External service mocking for reliable testing
- **Database Cleanup**: Automated test data cleanup between runs
- **Audio File Mocks**: Test audio files for upload validation

### Continuous Integration

- **Automated Testing**: GitHub Actions for CI/CD pipeline
- **Code Quality**: ESLint, Prettier, and TypeScript checking
- **Security Scanning**: Dependency vulnerability scanning
- **Performance Monitoring**: Bundle size and performance regression testing

## Security Considerations

### Authentication and Authorization

- **Password Security**: Bcrypt hashing with salt rounds
- **JWT Authentication**: Secure JSON Web Tokens with expiration
- **Role-Based Access**: Middleware-enforced role permissions
- **Judge Token Security**: Cryptographically secure unique tokens
- **Token Refresh**: Automatic token refresh for extended sessions

### Data Protection

- **Input Validation**: Server-side validation for all inputs with TanStack DB schema validation
- **SQL Injection Prevention**: TanStack DB's built-in query sanitization and type safety
- **XSS Protection**: Content Security Policy and input sanitization
- **File Upload Security**: File type validation and virus scanning
- **Data Encryption**: Sensitive data encryption at rest

### Audio File Security

- **Access Control**: Signed URLs for audio file access via MinIO
- **Storage Encryption**: Server-side encryption for MinIO storage
- **Upload Validation**: File format and content validation
- **Access Logging**: Audit trail for file access
- **Temporary URLs**: Time-limited access URLs for audio files

### Payment Security

- **PCI Compliance**: Stripe handles sensitive payment data
- **Webhook Verification**: Stripe webhook signature validation
- **Payment Intent Security**: Server-side payment confirmation
- **Invoice Protection**: User-specific invoice access control

### Infrastructure Security

- **HTTPS Enforcement**: SSL/TLS for all communications
- **Environment Variables**: Secure configuration management
- **Database Security**: TanStack DB encrypted connections and built-in access controls
- **MinIO Security**: Access key management and bucket policies
- **Monitoring**: Security event logging and alerting
