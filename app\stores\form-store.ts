// Form state management for multi-step wizard
import { create } from "zustand";
import { persist } from "zustand/middleware";

// Form data interfaces
export interface DirectorInfo {
  id: string;
  prefix?: string;
  firstName: string;
  lastName: string;
  isPrimary: boolean;
}

export interface SchoolInfo {
  ensembleName: string;
  schoolName: string;
  enrollment: number;
}

export interface ContestDetails {
  contestId: string;
  categoryId: string;
  classificationIds: string[];
}

export interface ContactInfo {
  phone: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  shippingAddress?: string;
  shippingCity?: string;
  shippingState?: string;
  shippingZipCode?: string;
  useDifferentShipping: boolean;
}

export interface SoloistInfo {
  id: string;
  name: string;
  instrument: string;
}

export interface MusicSelection {
  id: string;
  title: string;
  composer: string;
  arranger?: string;
  duration: string;
  recordingDate: Date;
  venue: string;
  audioFileUrl?: string;
  audioFileName?: string;
  audioFileSize?: number;
  soloists: SoloistInfo[];
  order: number;
}

// Complete form data
export interface RegistrationFormData {
  // Step 1: Director Information
  directors: DirectorInfo[];
  
  // Step 2: School Information
  school: SchoolInfo;
  
  // Step 3: Contest Details
  contest: ContestDetails;
  
  // Step 4: Contact Information
  contact: ContactInfo;
  
  // Step 5: Music Selections
  selections: MusicSelection[];
  
  // Form metadata
  isDraft: boolean;
  lastSaved: Date | null;
  currentStep: number;
  completedSteps: number[];
}

// Form actions interface
interface FormActions {
  // Director actions
  addDirector: (director: Omit<DirectorInfo, "id">) => void;
  updateDirector: (id: string, updates: Partial<DirectorInfo>) => void;
  removeDirector: (id: string) => void;
  setPrimaryDirector: (id: string) => void;
  
  // School actions
  updateSchool: (school: Partial<SchoolInfo>) => void;
  
  // Contest actions
  updateContest: (contest: Partial<ContestDetails>) => void;
  
  // Contact actions
  updateContact: (contact: Partial<ContactInfo>) => void;
  
  // Selection actions
  addSelection: (selection: Omit<MusicSelection, "id" | "order">) => void;
  updateSelection: (id: string, updates: Partial<MusicSelection>) => void;
  removeSelection: (id: string) => void;
  reorderSelections: (fromIndex: number, toIndex: number) => void;
  
  // Soloist actions
  addSoloist: (selectionId: string, soloist: Omit<SoloistInfo, "id">) => void;
  updateSoloist: (selectionId: string, soloistId: string, updates: Partial<SoloistInfo>) => void;
  removeSoloist: (selectionId: string, soloistId: string) => void;
  
  // Form management
  markStepComplete: (step: number) => void;
  markStepIncomplete: (step: number) => void;
  setCurrentStep: (step: number) => void;
  saveForm: () => void;
  resetForm: () => void;
  loadDraft: (data: Partial<RegistrationFormData>) => void;
  
  // Validation
  validateStep: (step: number) => { isValid: boolean; errors: string[] };
  validateForm: () => { isValid: boolean; errors: Record<number, string[]> };
}

// Initial state
const initialState: RegistrationFormData = {
  directors: [
    {
      id: Math.random().toString(36).substring(2, 15),
      firstName: "",
      lastName: "",
      isPrimary: true,
    },
  ],
  school: {
    ensembleName: "",
    schoolName: "",
    enrollment: 0,
  },
  contest: {
    contestId: "",
    categoryId: "",
    classificationIds: [],
  },
  contact: {
    phone: "",
    address: "",
    city: "",
    state: "",
    zipCode: "",
    useDifferentShipping: false,
  },
  selections: [],
  isDraft: true,
  lastSaved: null,
  currentStep: 1,
  completedSteps: [],
};

// Create the store
export const useFormStore = create<RegistrationFormData & FormActions>()(
  persist(
    (set, get) => ({
      ...initialState,
      
      // Director actions
      addDirector: (director) => {
        const id = Math.random().toString(36).substring(2, 15);
        set((state) => ({
          directors: [...state.directors, { ...director, id }],
        }));
      },
      
      updateDirector: (id, updates) =>
        set((state) => ({
          directors: state.directors.map((director) =>
            director.id === id ? { ...director, ...updates } : director
          ),
        })),
      
      removeDirector: (id) =>
        set((state) => {
          const directors = state.directors.filter((director) => director.id !== id);
          // If removing primary director, make first remaining director primary
          if (directors.length > 0 && !directors.some((d) => d.isPrimary)) {
            directors[0].isPrimary = true;
          }
          return { directors };
        }),
      
      setPrimaryDirector: (id) =>
        set((state) => ({
          directors: state.directors.map((director) => ({
            ...director,
            isPrimary: director.id === id,
          })),
        })),
      
      // School actions
      updateSchool: (school) =>
        set((state) => ({
          school: { ...state.school, ...school },
        })),
      
      // Contest actions
      updateContest: (contest) =>
        set((state) => ({
          contest: { ...state.contest, ...contest },
        })),
      
      // Contact actions
      updateContact: (contact) =>
        set((state) => ({
          contact: { ...state.contact, ...contact },
        })),
      
      // Selection actions
      addSelection: (selection) => {
        const id = Math.random().toString(36).substring(2, 15);
        const order = get().selections.length + 1;
        set((state) => ({
          selections: [...state.selections, { ...selection, id, order }],
        }));
      },
      
      updateSelection: (id, updates) =>
        set((state) => ({
          selections: state.selections.map((selection) =>
            selection.id === id ? { ...selection, ...updates } : selection
          ),
        })),
      
      removeSelection: (id) =>
        set((state) => {
          const selections = state.selections
            .filter((selection) => selection.id !== id)
            .map((selection, index) => ({ ...selection, order: index + 1 }));
          return { selections };
        }),
      
      reorderSelections: (fromIndex, toIndex) =>
        set((state) => {
          const selections = [...state.selections];
          const [removed] = selections.splice(fromIndex, 1);
          selections.splice(toIndex, 0, removed);
          
          // Update order numbers
          const reorderedSelections = selections.map((selection, index) => ({
            ...selection,
            order: index + 1,
          }));
          
          return { selections: reorderedSelections };
        }),
      
      // Soloist actions
      addSoloist: (selectionId, soloist) => {
        const id = Math.random().toString(36).substring(2, 15);
        set((state) => ({
          selections: state.selections.map((selection) =>
            selection.id === selectionId
              ? {
                  ...selection,
                  soloists: [...selection.soloists, { ...soloist, id }],
                }
              : selection
          ),
        }));
      },
      
      updateSoloist: (selectionId, soloistId, updates) =>
        set((state) => ({
          selections: state.selections.map((selection) =>
            selection.id === selectionId
              ? {
                  ...selection,
                  soloists: selection.soloists.map((soloist) =>
                    soloist.id === soloistId ? { ...soloist, ...updates } : soloist
                  ),
                }
              : selection
          ),
        })),
      
      removeSoloist: (selectionId, soloistId) =>
        set((state) => ({
          selections: state.selections.map((selection) =>
            selection.id === selectionId
              ? {
                  ...selection,
                  soloists: selection.soloists.filter((soloist) => soloist.id !== soloistId),
                }
              : selection
          ),
        })),
      
      // Form management
      markStepComplete: (step) =>
        set((state) => ({
          completedSteps: [...new Set([...state.completedSteps, step])],
        })),
      
      markStepIncomplete: (step) =>
        set((state) => ({
          completedSteps: state.completedSteps.filter((s) => s !== step),
        })),
      
      setCurrentStep: (step) =>
        set({ currentStep: step }),
      
      saveForm: () =>
        set({
          lastSaved: new Date(),
          isDraft: true,
        }),
      
      resetForm: () =>
        set(initialState),
      
      loadDraft: (data) =>
        set((state) => ({
          ...state,
          ...data,
        })),
      
      // Validation
      validateStep: (step) => {
        const state = get();
        const errors: string[] = [];
        
        switch (step) {
          case 1: // Directors
            if (state.directors.length === 0) {
              errors.push("At least one director is required");
            }
            state.directors.forEach((director, index) => {
              if (!director.firstName.trim()) {
                errors.push(`Director ${index + 1}: First name is required`);
              }
              if (!director.lastName.trim()) {
                errors.push(`Director ${index + 1}: Last name is required`);
              }
            });
            if (!state.directors.some((d) => d.isPrimary)) {
              errors.push("One director must be marked as primary");
            }
            break;
            
          case 2: // School
            if (!state.school.ensembleName.trim()) {
              errors.push("Ensemble name is required");
            }
            if (!state.school.schoolName.trim()) {
              errors.push("School name is required");
            }
            if (state.school.enrollment <= 0) {
              errors.push("School enrollment must be greater than 0");
            }
            break;
            
          case 3: // Contest
            if (!state.contest.contestId) {
              errors.push("Contest selection is required");
            }
            if (!state.contest.categoryId) {
              errors.push("Category selection is required");
            }
            if (state.contest.classificationIds.length === 0) {
              errors.push("At least one classification must be selected");
            }
            break;
            
          case 4: // Contact
            if (!state.contact.phone.trim()) {
              errors.push("Phone number is required");
            }
            if (!state.contact.address.trim()) {
              errors.push("Address is required");
            }
            if (!state.contact.city.trim()) {
              errors.push("City is required");
            }
            if (!state.contact.state.trim()) {
              errors.push("State is required");
            }
            if (!state.contact.zipCode.trim()) {
              errors.push("ZIP code is required");
            }
            if (state.contact.useDifferentShipping) {
              if (!state.contact.shippingAddress?.trim()) {
                errors.push("Shipping address is required");
              }
              if (!state.contact.shippingCity?.trim()) {
                errors.push("Shipping city is required");
              }
              if (!state.contact.shippingState?.trim()) {
                errors.push("Shipping state is required");
              }
              if (!state.contact.shippingZipCode?.trim()) {
                errors.push("Shipping ZIP code is required");
              }
            }
            break;
            
          case 5: // Selections
            if (state.selections.length === 0) {
              errors.push("At least one music selection is required");
            }
            state.selections.forEach((selection, index) => {
              if (!selection.title.trim()) {
                errors.push(`Selection ${index + 1}: Title is required`);
              }
              if (!selection.composer.trim()) {
                errors.push(`Selection ${index + 1}: Composer is required`);
              }
              if (!selection.duration.trim()) {
                errors.push(`Selection ${index + 1}: Duration is required`);
              }
              if (!selection.venue.trim()) {
                errors.push(`Selection ${index + 1}: Venue is required`);
              }
            });
            break;
        }
        
        return { isValid: errors.length === 0, errors };
      },
      
      validateForm: () => {
        const errors: Record<number, string[]> = {};
        let isValid = true;
        
        for (let step = 1; step <= 5; step++) {
          const stepValidation = get().validateStep(step);
          if (!stepValidation.isValid) {
            errors[step] = stepValidation.errors;
            isValid = false;
          }
        }
        
        return { isValid, errors };
      },
    }),
    {
      name: "registration-form",
      // Persist the entire form state
      partialize: (state) => ({
        directors: state.directors,
        school: state.school,
        contest: state.contest,
        contact: state.contact,
        selections: state.selections,
        isDraft: state.isDraft,
        lastSaved: state.lastSaved,
        currentStep: state.currentStep,
        completedSteps: state.completedSteps,
      }),
    }
  )
);

// Selector hooks
export const useDirectors = () => useFormStore((state) => state.directors);
export const useSchoolInfo = () => useFormStore((state) => state.school);
export const useContestDetails = () => useFormStore((state) => state.contest);
export const useContactInfo = () => useFormStore((state) => state.contact);
export const useSelections = () => useFormStore((state) => state.selections);
export const useFormProgress = () => useFormStore((state) => ({
  currentStep: state.currentStep,
  completedSteps: state.completedSteps,
  isDraft: state.isDraft,
  lastSaved: state.lastSaved,
}));