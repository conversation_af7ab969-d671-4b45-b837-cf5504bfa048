// Simple test runner for authentication system
import { setupTestData, testAuthFlow } from './app/lib/test-setup.js';

async function runTests() {
  console.log('🚀 Starting Music Judging App Authentication Tests\n');
  
  try {
    // Setup test data
    await setupTestData();
    
    console.log('\n' + '='.repeat(50));
    
    // Test authentication flow
    await testAuthFlow();
    
    console.log('\n' + '='.repeat(50));
    console.log('✅ All tests completed successfully!');
    console.log('\n🌐 You can now run the app with: npm run dev');
    console.log('📝 Try logging in with the test accounts above');
    
  } catch (error) {
    console.error('\n❌ Tests failed:', error);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests();
}