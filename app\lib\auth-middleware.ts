// Authentication middleware for route protection
import { redirect } from "@tanstack/react-router";
import { getUserFromToken, verifyToken } from "./auth";
import type { User } from "./schema";

// Auth context type
export interface AuthContext {
  user: User | null;
  isAuthenticated: boolean;
  isAdmin: boolean;
  token: string | null;
}

// Get auth token from various sources
export function getAuthToken(): string | null {
  // Check localStorage first
  if (typeof window !== "undefined") {
    const token = localStorage.getItem("auth-token");
    if (token) return token;
  }

  // Check cookies (for SSR)
  if (typeof document !== "undefined") {
    const cookies = document.cookie.split(";");
    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split("=");
      if (name === "auth-token") {
        return decodeURIComponent(value);
      }
    }
  }

  return null;
}

// Set auth token in storage
export function setAuthToken(token: string): void {
  if (typeof window !== "undefined") {
    localStorage.setItem("auth-token", token);
    
    // Also set as httpOnly cookie for SSR
    const expires = new Date();
    expires.setDate(expires.getDate() + 7); // 7 days
    document.cookie = `auth-token=${encodeURIComponent(token)}; expires=${expires.toUTCString()}; path=/; secure; samesite=strict`;
  }
}

// Remove auth token from storage
export function removeAuthToken(): void {
  if (typeof window !== "undefined") {
    localStorage.removeItem("auth-token");
    
    // Remove cookie
    document.cookie = "auth-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
  }
}

// Get current auth context
export async function getAuthContext(): Promise<AuthContext> {
  try {
    console.log("Getting auth context...");
    const token = getAuthToken();
    console.log("Token found:", !!token);
    
    if (!token) {
      console.log("No token, returning unauthenticated state");
      return {
        user: null,
        isAuthenticated: false,
        isAdmin: false,
        token: null,
      };
    }

    console.log("Getting user from token...");
    const user = await getUserFromToken(token);
    console.log("User from token:", !!user);
    
    if (!user) {
      // Token is invalid, remove it
      console.log("Invalid token, removing...");
      removeAuthToken();
      return {
        user: null,
        isAuthenticated: false,
        isAdmin: false,
        token: null,
      };
    }

    console.log("Returning authenticated state");
    return {
      user,
      isAuthenticated: true,
      isAdmin: user.isAdmin,
      token,
    };
  } catch (error) {
    console.error("Error in getAuthContext:", error);
    return {
      user: null,
      isAuthenticated: false,
      isAdmin: false,
      token: null,
    };
  }
}

// Route guard for authenticated routes
export async function requireAuth(): Promise<AuthContext> {
  const authContext = await getAuthContext();
  
  if (!authContext.isAuthenticated) {
    throw redirect({
      to: "/login",
      search: {
        redirect: window.location.pathname,
      },
    });
  }
  
  return authContext;
}

// Route guard for admin routes
export async function requireAdmin(): Promise<AuthContext> {
  const authContext = await requireAuth();
  
  if (!authContext.isAdmin) {
    throw redirect({
      to: "/dashboard",
    });
  }
  
  return authContext;
}

// Route guard for guest routes (redirect if already authenticated)
export async function requireGuest(): Promise<void> {
  const authContext = await getAuthContext();
  
  if (authContext.isAuthenticated) {
    throw redirect({
      to: authContext.isAdmin ? "/admin" : "/dashboard",
    });
  }
}

// Logout utility
export async function logout(): Promise<void> {
  removeAuthToken();
  
  // Redirect to login page
  if (typeof window !== "undefined") {
    window.location.href = "/login";
  }
}

// Check if user has permission to access resource
export function hasPermission(
  authContext: AuthContext,
  resource: "admin" | "user" | "judge",
  action: "read" | "write" | "delete" = "read"
): boolean {
  if (!authContext.isAuthenticated) {
    return false;
  }

  switch (resource) {
    case "admin":
      return authContext.isAdmin;
    
    case "user":
      return true; // All authenticated users can access user resources
    
    case "judge":
      // For now, judges don't need accounts, they use unique tokens
      // This might be used for admin managing judges
      return authContext.isAdmin;
    
    default:
      return false;
  }
}

// Middleware for API routes (if using server-side rendering)
export async function authMiddleware(request: Request): Promise<{
  user: User | null;
  isAuthenticated: boolean;
  isAdmin: boolean;
}> {
  const authHeader = request.headers.get("Authorization");
  const token = authHeader?.replace("Bearer ", "") || 
                request.headers.get("Cookie")?.split("auth-token=")[1]?.split(";")[0];

  if (!token) {
    return {
      user: null,
      isAuthenticated: false,
      isAdmin: false,
    };
  }

  const user = await getUserFromToken(token);
  
  return {
    user,
    isAuthenticated: !!user,
    isAdmin: user?.isAdmin || false,
  };
}

// Helper to create protected API handler
export function withAuth<T extends any[]>(
  handler: (authContext: AuthContext, ...args: T) => Promise<Response> | Response
) {
  return async (request: Request, ...args: T): Promise<Response> => {
    const authContext = await authMiddleware(request);
    
    if (!authContext.isAuthenticated) {
      return new Response(
        JSON.stringify({ error: "Authentication required" }),
        {
          status: 401,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    return handler(authContext as AuthContext, ...args);
  };
}

// Helper to create admin-only API handler
export function withAdminAuth<T extends any[]>(
  handler: (authContext: AuthContext, ...args: T) => Promise<Response> | Response
) {
  return async (request: Request, ...args: T): Promise<Response> => {
    const authContext = await authMiddleware(request);
    
    if (!authContext.isAuthenticated) {
      return new Response(
        JSON.stringify({ error: "Authentication required" }),
        {
          status: 401,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    if (!authContext.isAdmin) {
      return new Response(
        JSON.stringify({ error: "Admin access required" }),
        {
          status: 403,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    return handler(authContext as AuthContext, ...args);
  };
}