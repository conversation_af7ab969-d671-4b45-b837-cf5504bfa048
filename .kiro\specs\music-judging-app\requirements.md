# Requirements Document

## Introduction

The Music Judging App is a comprehensive platform designed to facilitate music competitions by providing role-based dashboards for administrators, participants, and judges. The system supports two main contest types: Mark of Excellence (MoE) and Citation of Excellence (CoE), each with specific categories, classifications, and pricing structures. The platform enables multi-step registration, audio submissions, phased judging workflows, and comprehensive results management with payment processing and download code distribution.

## Requirements

### Requirement 1: Contest Structure and Management

**User Story:** As an administrator, I want to manage contest structures with categories and classifications, so that I can organize competitions according to established frameworks.

#### Acceptance Criteria

1. WHEN an admin configures contests THEN the system SHALL support Mark of Excellence and Citation of Excellence contest types
2. WHEN setting up MoE categories THEN the system SHALL include National Choral Honors, National Jazz Honors, National Orchestra Honors, National Percussion Honors, and National Wind Band Honors with their specific classifications
3. WHEN setting up CoE categories THEN the system SHALL include Citation of Excellence - Band and Citation of Excellence - Orchestra with their specific classifications
4. WHEN configuring pricing THEN the system SHALL set MoE at $400 per classification and CoE at $325 per entry
5. WHEN managing Wind Band Honors THEN the system SHALL allow groups to register for both A-type classifications and New Music Division simultaneously

### Requirement 2: User Registration and Authentication

**User Story:** As a participant, I want to create and manage my account securely, so that I can access the platform and manage my entries.

#### Acceptance Criteria

1. WHEN a user registers THEN the system SHALL require email and secure password with validation
2. WHEN a user logs in THEN the system SHALL authenticate using email and password
3. WHEN a user forgets password THEN the system SHALL send password reset email
4. WHEN a user manages account THEN the system SHALL allow editing username and password
5. WHEN accessing account THEN the system SHALL display current and previous year entries with appropriate status information

### Requirement 3: Multi-Step Entry Registration

**User Story:** As a participant, I want to register my ensemble through a guided multi-step process, so that I can submit complete and accurate entry information.

#### Acceptance Criteria

1. WHEN starting registration THEN the system SHALL present a 6-step form with validation at each step
2. WHEN entering director information THEN the system SHALL collect prefix, first name, last name, and allow multiple directors
3. WHEN entering school information THEN the system SHALL collect ensemble name, school name, and enrollment size
4. WHEN selecting contest details THEN the system SHALL allow contest selection, category selection, classification selection with multi-select for Wind Band Honors, and display registration fees
5. WHEN entering contact information THEN the system SHALL collect phone, mailing address, and optional different shipping address
6. WHEN entering music selections THEN the system SHALL collect title, composer/arranger, duration, recording date, venue, audio file, and soloist information when applicable

### Requirement 4: Audio File Management and Validation

**User Story:** As a participant, I want to upload audio files for my entries, so that judges can evaluate my submissions.

#### Acceptance Criteria

1. WHEN uploading audio THEN the system SHALL accept .wav and .mp3 formats
2. WHEN validating uploads THEN the system SHALL check file format and provide clear error messages for invalid files
3. WHEN submitting without audio THEN the system SHALL allow registration completion and alert users about missing files
4. WHEN managing entries THEN the system SHALL indicate which entries are missing audio files
5. WHEN storing audio THEN the system SHALL maintain files for current year and delete files older than 2 years

### Requirement 5: Payment Processing and Invoice Management

**User Story:** As a participant, I want to pay for my entries securely, so that I can complete my registration and participate in competitions.

#### Acceptance Criteria

1. WHEN viewing entries THEN the system SHALL display invoice amounts and provide downloadable PDF invoices
2. WHEN processing payments THEN the system SHALL integrate with Stripe for credit card processing
3. WHEN sharing payment THEN the system SHALL generate payment links for others to pay on behalf of the user
4. WHEN payment is complete THEN the system SHALL update entry status and send confirmation
5. WHEN results are not live THEN the system SHALL show payment status and allow editing if registration is open

### Requirement 6: Judge Access and Video Introduction

**User Story:** As a judge, I want to access my assignments through a unique link and create an introduction video, so that I can begin the judging process.

#### Acceptance Criteria

1. WHEN accessing judge link THEN the system SHALL not require account creation, only unique link access
2. WHEN first visiting THEN the system SHALL prompt judge to create video introduction
3. WHEN creating introduction THEN the system SHALL provide preview, re-record, and save functionality
4. WHEN accessing dashboard THEN the system SHALL display all current assignments and overall progress
5. WHEN reviewing introduction THEN the system SHALL allow judges to view and update their introduction video

### Requirement 7: Judge Assignment and Phase Management

**User Story:** As an administrator, I want to assign judges to categories and phases, so that I can manage the judging workflow effectively.

#### Acceptance Criteria

1. WHEN creating judge assignments THEN the system SHALL allow assignment to categories with all or specific classifications
2. WHEN setting phases THEN the system SHALL support No Phase, Phase 1, and Phase 2 assignments
3. WHEN assigning specific groups THEN the system SHALL allow targeting individual entries or all entries in a classification
4. WHEN managing judges THEN the system SHALL allow accessing judge pages, reviewing intro videos, and sending template emails
5. WHEN deactivating judges THEN the system SHALL set inactive status while preserving judge ID and video links

### Requirement 8: Judging Workflow and Scoring

**User Story:** As a judge, I want to evaluate entries with scoring, notes, and voice memos, so that I can provide comprehensive feedback.

#### Acceptance Criteria

1. WHEN viewing group THEN the system SHALL provide audio player with selection navigation and memo recording capabilities
2. WHEN leaving feedback THEN the system SHALL support written comments and voice memo recordings with playback and deletion
3. WHEN scoring entries THEN the system SHALL provide numerical score fields and selection checkboxes based on phase and contest type
4. WHEN identifying soloists THEN the system SHALL allow selection of outstanding soloists by instrument and timestamp
5. WHEN completing evaluation THEN the system SHALL save progress and allow return to incomplete judgments

### Requirement 9: Results Management and Winner Selection

**User Story:** As a judge, I want to select winners and advancing entries based on scoring and qualitative assessment, so that I can determine competition outcomes.

#### Acceptance Criteria

1. WHEN viewing classification dashboard THEN the system SHALL show suggested top percentiles based on scores (50% for Phase 1, 25% for National/Citation, next 25% for Commended)
2. WHEN selecting winners THEN the system SHALL provide checkboxes for National/Citation awards and Commended awards based on contest type
3. WHEN Phase 1 judging THEN the system SHALL allow selection of groups to advance to next phase
4. WHEN completing judging THEN the system SHALL calculate aggregate scores and provide winner selection interface
5. WHEN results are finalized THEN the system SHALL generate winner announcements and result summaries

### Requirement 10: Admin Results and Data Management

**User Story:** As an administrator, I want to manage results and export data, so that I can distribute awards and maintain records.

#### Acceptance Criteria

1. WHEN downloading results THEN the system SHALL generate CSV files for results and soloists by selected year
2. WHEN managing download codes THEN the system SHALL allow CSV upload to assign download codes to all entries by classification
3. WHEN controlling results visibility THEN the system SHALL toggle results on/off with custom messages
4. WHEN managing entries THEN the system SHALL provide table view with edit, archive, and export capabilities
5. WHEN managing users THEN the system SHALL show entry history, login activity, and provide user impersonation for troubleshooting

### Requirement 11: Registration Control and Communication

**User Story:** As an administrator, I want to control registration periods and communicate with users, so that I can manage competition timelines effectively.

#### Acceptance Criteria

1. WHEN controlling registration THEN the system SHALL toggle registration on/off with custom messages for closed periods
2. WHEN managing rules THEN the system SHALL allow viewing and updating rules and guidelines with external link integration
3. WHEN communicating with judges THEN the system SHALL provide email templates for instructions, reminders, and updates
4. WHEN sending reminders THEN the system SHALL trigger reminder emails to users with incomplete entries
5. WHEN managing contest settings THEN the system SHALL allow price adjustments and category/classification management

### Requirement 12: Results Display and User Experience

**User Story:** As a participant, I want to view my results and access additional materials, so that I can see my competition outcomes and access earned content.

#### Acceptance Criteria

1. WHEN results are live THEN the system SHALL display judge voice memos, notes, and introduction videos
2. WHEN viewing results THEN the system SHALL show download codes or "coming soon" message
3. WHEN displaying awards THEN the system SHALL indicate National/Citation winners and Commended recipients
4. WHEN accessing historical data THEN the system SHALL allow toggling between years with audio deletion notice for entries older than 2 years
5. WHEN results are published THEN the system SHALL provide link to public results page displaying all winners