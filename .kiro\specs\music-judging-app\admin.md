# Admin Requirements

- Manage Contests
    - set each contest prices
    - manage categories
        - manage classifications
    - turn Registrations on/off
        - provide a message for when registrations are turned off
    - turn Results on/off
        - provide a message for when results are live
    - View and Update Rules & Guidelines 
- Manage Judges
    - create new judges
    - manage their assignments
        - assignment:
            - category
                - all classifications or specific classification in category
                - assign phase if needed
                    - No Phase
                    - Phase 1
                    - Phase 2
                - be able to assign specific groups if needed (leave empty for all groups)
    - access the judges pages
    - review judges video intro
    - remove judges
    - email judges 
        - email all active judges or selected judges with select templates
            - Intructions & Tutorial
            - Reminders
            - Updates
    - "delete" judges - set the judge as inactive to keep the judges id and link to videos
- View and Manage Entries
    - table to view all the entries per category/classifications
    - be able to edit and update entries 
    - be able to archive entries
    - be able to trigger reminders to user who submitted entry if needed
    - be able to export entries
- View and Manage Users
    - view list of users
    - be able to see number of entries they have in current year and past years
    - view when they last logged in
    - be able to impersonate user to see what they see from dashboard
    - create admin user
- Download CSV of Results and CSV of Soloists of selected year
- Add Download Codes to all entries 
    - upload a csv file that assigns download codes to all entries for the current year.
        - a download code gets assigned to each classifcation they entered